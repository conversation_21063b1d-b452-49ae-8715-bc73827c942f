# 🚀 BeyondSmart ERP System - نظام إدارة موارد المؤسسة

نظام ERP متكامل يتكون من Backend (ASP.NET Core 8) و Frontend (React) لإدارة الأعمال بشكل شامل.

## 📋 المكونات الرئيسية

### 🔧 Backend - BeyondSmart.API
- **Framework:** ASP.NET Core 8 Web API
- **Database:** Entity Framework Core مع InMemory Database
- **Authentication:** JWT Bearer Token
- **Documentation:** Swagger/OpenAPI
- **CORS:** مُفعل للتكامل مع React

### 🎨 Frontend - React Application
- **Framework:** React 18 مع Vite
- **UI Library:** Tailwind CSS + Radix UI
- **Routing:** React Router DOM
- **State Management:** Context API + Custom Hooks
- **Charts:** Recharts
- **Icons:** Lucide React

## 🏗️ هيكل المشروع

```
BeyondSmart-ERP/
├── BeyondSmart.API/          # Backend API
│   ├── Controllers/          # API Controllers
│   ├── Models/              # Entity Models
│   ├── Data/                # Database Context
│   ├── Services/            # Business Services
│   └── DTOs/                # Data Transfer Objects
├── src/                     # Frontend React App
│   ├── components/          # UI Components
│   ├── pages/              # Page Components
│   ├── contexts/           # React Contexts
│   ├── hooks/              # Custom Hooks
│   ├── services/           # API Services
│   └── lib/                # Utilities
└── public/                 # Static Assets
```

## 🗄️ قاعدة البيانات

### الكيانات (Entities):
- **User** - المستخدمين والأدوار
- **Client** - العملاء
- **Quotation** - عروض الأسعار
- **Project** - المشاريع
- **InventoryItem** - عناصر المخزون
- **MaintenanceRequest** - طلبات الصيانة

## 🔐 نظام المصادقة

### الأدوار المتاحة:
- **Admin** - مدير النظام (صلاحيات كاملة)
- **Sales** - مندوب المبيعات (عروض الأسعار والعملاء)
- **Warehouse** - أمين المخزن (إدارة المخزون)

### بيانات الدخول الافتراضية:
| المستخدم | البريد الإلكتروني | كلمة المرور | الدور |
|---------|------------------|------------|-------|
| Admin | <EMAIL> | admin123 | Admin |
| Sales | <EMAIL> | sales123 | Sales |
| Warehouse | <EMAIL> | warehouse123 | Warehouse |

## 🚀 تشغيل المشروع

### المتطلبات:
- .NET 8.0 SDK
- Node.js 18+
- npm أو yarn

### 1. تشغيل Backend:
```bash
cd BeyondSmart.API
dotnet restore
dotnet build
dotnet run
```
**Backend URL:** http://localhost:5181

### 2. تشغيل Frontend:
```bash
npm install
npm run dev
```
**Frontend URL:** http://localhost:5173

## 📊 الميزات الرئيسية

### 🏠 لوحة التحكم
- إحصائيات شاملة للأعمال
- رسوم بيانية تفاعلية
- إجراءات سريعة
- تنبيهات المخزون المنخفض

### 👥 إدارة العملاء
- إضافة وتعديل بيانات العملاء
- تتبع تاريخ التعاملات
- ربط العملاء بالمشاريع

### 📋 عروض الأسعار
- إنشاء عروض أسعار احترافية
- حساب تلقائي للأسعار والخصومات
- تحويل العروض إلى مشاريع
- طباعة وتصدير PDF

### 🏗️ إدارة المشاريع
- تتبع حالة المشاريع
- ربط المشاريع بعروض الأسعار
- إدارة المواعيد والمهام
- تقارير التقدم

### 📦 إدارة المخزون
- تتبع المواد والأدوات
- تنبيهات المخزون المنخفض
- وحدات قياس متعددة (متر مربع، قطعة)
- تقارير المخزون

### 🔧 طلبات الصيانة
- تسجيل طلبات الصيانة
- ربط الطلبات بالمشاريع
- تتبع حالة الطلبات
- تاريخ كامل للصيانة

## 🔗 API Endpoints

### Authentication:
- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/register` - تسجيل مستخدم جديد

### Clients:
- `GET /api/clients` - جلب العملاء
- `POST /api/clients` - إضافة عميل
- `PUT /api/clients/{id}` - تحديث عميل
- `DELETE /api/clients/{id}` - حذف عميل

### Quotations:
- `GET /api/quotations` - جلب عروض الأسعار
- `POST /api/quotations` - إنشاء عرض سعر
- `PUT /api/quotations/{id}` - تحديث عرض سعر
- `DELETE /api/quotations/{id}` - حذف عرض سعر

### Projects:
- `GET /api/projects` - جلب المشاريع
- `POST /api/projects` - إنشاء مشروع
- `PUT /api/projects/{id}` - تحديث مشروع
- `DELETE /api/projects/{id}` - حذف مشروع

### Inventory:
- `GET /api/inventoryitems` - جلب المخزون
- `POST /api/inventoryitems` - إضافة عنصر
- `PUT /api/inventoryitems/{id}` - تحديث عنصر
- `DELETE /api/inventoryitems/{id}` - حذف عنصر

### Maintenance:
- `GET /api/maintenancerequests` - جلب طلبات الصيانة
- `POST /api/maintenancerequests` - إنشاء طلب صيانة
- `PUT /api/maintenancerequests/{id}` - تحديث طلب
- `DELETE /api/maintenancerequests/{id}` - حذف طلب

## 🛠️ التقنيات المستخدمة

### Backend:
- ASP.NET Core 8
- Entity Framework Core
- JWT Authentication
- BCrypt.Net (تشفير كلمات المرور)
- Swagger/OpenAPI
- CORS

### Frontend:
- React 18
- Vite (Build Tool)
- Tailwind CSS
- Radix UI Components
- React Router DOM
- Recharts (للرسوم البيانية)
- Framer Motion (للحركات)
- Lucide React (للأيقونات)

## 📱 التوافق

- ✅ متوافق مع جميع المتصفحات الحديثة
- ✅ تصميم متجاوب (Responsive)
- ✅ دعم الأجهزة المحمولة
- ✅ واجهة باللغة العربية والإنجليزية

## 🔒 الأمان

- مصادقة JWT آمنة
- تشفير كلمات المرور
- تحكم في الصلاحيات حسب الأدوار
- حماية CORS
- التحقق من صحة البيانات

## 📈 الأداء

- تحميل سريع مع Vite
- تحسين الصور والأصول
- Lazy Loading للمكونات
- Cache للبيانات
- استجابة سريعة للـ API

## 🚀 النشر

### Backend:
- يمكن نشره على Azure, AWS, أو أي خدمة استضافة .NET
- دعم Docker
- قاعدة بيانات SQL Server للإنتاج

### Frontend:
- يمكن نشره على Vercel, Netlify, أو أي خدمة استضافة
- Build محسن للإنتاج
- CDN Support

## 📞 الدعم

للدعم الفني أو الاستفسارات، يرجى التواصل معنا.

---

**تم تطوير هذا النظام بواسطة Beyond Smart Tech** 🚀
