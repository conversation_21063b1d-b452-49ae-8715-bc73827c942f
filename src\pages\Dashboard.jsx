import React, { useMemo, useState } from 'react';
    import { Link, useNavigate } from 'react-router-dom';
    import { Helmet } from 'react-helmet';
    import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
    import { Button } from '@/components/ui/button';
    import { DollarSign, FileText, CheckCircle, Package, PlusCircle, Wrench, Users, Loader2, TrendingUp, Calendar } from 'lucide-react';
    import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
    import { useAuth } from '@/contexts/AuthContext';
    import { useProjects, useQuotations, useClients, useInventory } from '@/hooks/useApi';
    import DateRangeFilter from '@/components/DateRangeFilter';
    import { motion } from 'framer-motion';

    const Dashboard = () => {
      const { user } = useAuth();
      const navigate = useNavigate();
      const [dateRange, setDateRange] = useState(null);

      // Fetch data from API
      const { projects, loading: projectsLoading } = useProjects();
      const { quotations, loading: quotationsLoading } = useQuotations();
      const { clients, loading: clientsLoading } = useClients();
      const { inventory, loading: inventoryLoading } = useInventory();

      const isLoading = projectsLoading || quotationsLoading || clientsLoading || inventoryLoading;

      // Filter data based on date range
      const filteredData = useMemo(() => {
        if (!dateRange) {
          return { projects, quotations, clients };
        }

        const { startDate, endDate } = dateRange;

        const filteredProjects = projects?.filter(project => {
          const projectDate = new Date(project.createdAt || project.startDate);
          return projectDate >= startDate && projectDate <= endDate;
        });

        const filteredQuotations = quotations?.filter(quotation => {
          const quotationDate = new Date(quotation.createdAt || quotation.date);
          return quotationDate >= startDate && quotationDate <= endDate;
        });

        return {
          projects: filteredProjects,
          quotations: filteredQuotations,
          clients // العملاء لا يتم فلترتهم بالتاريخ
        };
      }, [projects, quotations, clients, dateRange]);

      const dashboardStats = useMemo(() => {
        const currentProjects = filteredData.projects || [];
        const currentQuotations = filteredData.quotations || [];
        const currentClients = filteredData.clients || [];

        if (!currentProjects || !currentQuotations) return {
          totalRevenue: 0,
          activeProjects: 0,
          sentQuotationsCount: 0,
          conversionRate: 0,
          confirmedProjectsCount: 0,
          totalClients: 0,
          lowStockItems: 0,
        };

        const activeProjects = currentProjects.filter(p => p.status === 'Ongoing').length;
        const completedProjects = currentProjects.filter(p => p.status === 'Completed').length;
        const totalRevenue = currentQuotations.reduce((acc, q) => acc + (q.total || 0), 0);
        const conversionRate = currentQuotations.length > 0 ? (currentProjects.length / currentQuotations.length) * 100 : 0;
        const lowStockItems = inventory ? inventory.filter(item => item.quantity < 10).length : 0;

        return {
            totalRevenue,
            activeProjects,
            sentQuotationsCount: currentQuotations.length,
            conversionRate,
            confirmedProjectsCount: completedProjects,
            totalClients: currentClients.length,
            lowStockItems,
        };
      }, [filteredData, inventory]);
      
      const chartData = useMemo(() => {
        const monthlyData = {};
        quotations.forEach(q => {
            const month = new Date(q.date).toLocaleString('default', { month: 'short' });
            if (!monthlyData[month]) {
                monthlyData[month] = { name: month, Sales: 0, Projects: 0 };
            }
            if(projects.some(p => p.quotationId === q.id)){
                const subtotal = q.items.reduce((itemAcc, item) => itemAcc + (item.quantity * item.price), 0);
                const discountAmount = q.discountType === 'percentage' ? subtotal * (q.discount / 100) : q.discount;
                monthlyData[month].Sales += subtotal - discountAmount;
            }
        });
        projects.forEach(p => {
             const month = new Date(p.date).toLocaleString('default', { month: 'short' });
             if(monthlyData[month]){
                 monthlyData[month].Projects += 1;
             }
        });
        return Object.values(monthlyData);
      }, [projects, quotations]);


      if (isLoading) {
        return (
          <div className="flex items-center justify-center min-h-[400px]">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading dashboard...</span>
          </div>
        );
      }

      return (
        <>
          <Helmet>
            <title>Dashboard - Beyond Smart Tech ERP</title>
            <meta name="description" content="ERP System Dashboard for Beyond Smart Tech" />
          </Helmet>
          <div className="space-y-8">
            <div>
                <h1 className="text-3xl font-bold tracking-tight">Good Morning, {user?.name}!</h1>
                <p className="text-muted-foreground">Here's a summary of your business activities.</p>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                    <Button asChild size="lg" className="w-full justify-start p-6">
                        <Link to="/quotations/new">
                            <PlusCircle className="mr-4 h-6 w-6" />
                            <div className="text-left">
                                <p className="font-semibold">New Quotation</p>
                                <p className="font-normal text-sm text-primary-foreground/80">Create a new quote for a client</p>
                            </div>
                        </Link>
                    </Button>
                    <Button size="lg" className="w-full justify-start p-6" variant="secondary" onClick={() => navigate('/maintenance', { state: { openDialog: true } })}>
                        <Wrench className="mr-4 h-6 w-6" />
                         <div className="text-left">
                            <p className="font-semibold">New Maintenance</p>
                            <p className="font-normal text-sm text-secondary-foreground/80">Log a new service request</p>
                        </div>
                    </Button>
                </CardContent>
            </Card>

            {/* Date Range Filter */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              <DateRangeFilter
                onDateRangeChange={setDateRange}
                className="mb-6"
              />
            </motion.div>

            {/* Period Summary */}
            {dateRange && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4 }}
                className="mb-6"
              >
                <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Calendar className="h-5 w-5 text-blue-600" />
                      <div>
                        <p className="text-sm font-medium text-blue-900">
                          عرض البيانات للفترة: {dateRange.startDate.toLocaleDateString('ar-SA')} - {dateRange.endDate.toLocaleDateString('ar-SA')}
                        </p>
                        <p className="text-xs text-blue-700">
                          {filteredData.projects?.length || 0} مشروع، {filteredData.quotations?.length || 0} عرض سعر
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {/* Total Revenue Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1, duration: 0.5 }}
                whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
              >
                <Card className="bg-gradient-to-br from-green-50 to-emerald-100 border-green-200 hover:shadow-lg transition-all duration-300">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-green-800">إجمالي الإيرادات</CardTitle>
                    <div className="p-2 bg-green-500 rounded-full">
                      <DollarSign className="h-4 w-4 text-white" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-900">${dashboardStats.totalRevenue.toFixed(2)}</div>
                    <p className="text-xs text-green-700 flex items-center gap-1">
                      <TrendingUp className="h-3 w-3" />
                      من {dashboardStats.sentQuotationsCount} عرض سعر
                    </p>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Active Projects Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
              >
                <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 hover:shadow-lg transition-all duration-300">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-blue-800">المشاريع النشطة</CardTitle>
                    <div className="p-2 bg-blue-500 rounded-full">
                      <CheckCircle className="h-4 w-4 text-white" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-900">{dashboardStats.activeProjects}</div>
                    <p className="text-xs text-blue-700">قيد التنفيذ حالياً</p>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Total Clients Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.5 }}
                whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
              >
                <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 hover:shadow-lg transition-all duration-300">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-purple-800">إجمالي العملاء</CardTitle>
                    <div className="p-2 bg-purple-500 rounded-full">
                      <Users className="h-4 w-4 text-white" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-purple-900">{dashboardStats.totalClients}</div>
                    <p className="text-xs text-purple-700">عملاء مسجلين</p>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Low Stock Items Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.5 }}
                whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
              >
                <Card className="bg-gradient-to-br from-orange-50 to-red-100 border-orange-200 hover:shadow-lg transition-all duration-300">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-orange-800">المخزون المنخفض</CardTitle>
                    <div className="p-2 bg-orange-500 rounded-full">
                      <Package className="h-4 w-4 text-white" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-orange-900">{dashboardStats.lowStockItems}</div>
                    <p className="text-xs text-orange-700">أصناف أقل من 10 وحدات</p>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Additional Stats Row */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mt-6">
              {/* Quotations Sent Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.5 }}
                whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
              >
                <Card className="bg-gradient-to-br from-indigo-50 to-indigo-100 border-indigo-200 hover:shadow-lg transition-all duration-300">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-indigo-800">عروض الأسعار المرسلة</CardTitle>
                    <div className="p-2 bg-indigo-500 rounded-full">
                      <FileText className="h-4 w-4 text-white" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-indigo-900">+{dashboardStats.sentQuotationsCount}</div>
                    <p className="text-xs text-indigo-700">إجمالي العروض المنشأة</p>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Conversion Rate Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.5 }}
                whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
              >
                <Card className="bg-gradient-to-br from-teal-50 to-teal-100 border-teal-200 hover:shadow-lg transition-all duration-300">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-teal-800">معدل التحويل</CardTitle>
                    <div className="p-2 bg-teal-500 rounded-full">
                      <CheckCircle className="h-4 w-4 text-white" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-teal-900">{dashboardStats.conversionRate.toFixed(1)}%</div>
                    <p className="text-xs text-teal-700">من جميع العروض المرسلة</p>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Completed Projects Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7, duration: 0.5 }}
                whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
              >
                <Card className="bg-gradient-to-br from-emerald-50 to-green-100 border-emerald-200 hover:shadow-lg transition-all duration-300">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-emerald-800">المشاريع المكتملة</CardTitle>
                    <div className="p-2 bg-emerald-500 rounded-full">
                      <CheckCircle className="h-4 w-4 text-white" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-emerald-900">{dashboardStats.confirmedProjectsCount}</div>
                    <p className="text-xs text-emerald-700">مشاريع منجزة بنجاح</p>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
            <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Sales Overview</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" vertical={false} />
                        <XAxis dataKey="name" tickLine={false} axisLine={false} />
                        <YAxis tickLine={false} axisLine={false} tickFormatter={(value) => `${value/1000}k`} />
                        <Tooltip cursor={{ fill: 'hsl(var(--muted))' }} contentStyle={{backgroundColor: 'hsl(var(--card))', border: '1px solid hsl(var(--border))', borderRadius: 'var(--radius)'}}/>
                        <Bar dataKey="Sales" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>New Projects Growth</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={chartData}>
                         <CartesianGrid strokeDasharray="3 3" vertical={false} />
                        <XAxis dataKey="name" tickLine={false} axisLine={false} />
                        <YAxis tickLine={false} axisLine={false} />
                        <Tooltip cursor={{ fill: 'hsl(var(--muted))' }} contentStyle={{backgroundColor: 'hsl(var(--card))', border: '1px solid hsl(var(--border))', borderRadius: 'var(--radius)'}}/>
                        <Line type="monotone" dataKey="Projects" stroke="hsl(var(--primary))" strokeWidth={2} />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
            </div>
          </div>
        </>
      );
    };

    export default Dashboard;