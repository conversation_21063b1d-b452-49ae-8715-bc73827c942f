
import React, { useState } from 'react';
    import { NavLink } from 'react-router-dom';
    import { motion, AnimatePresence } from 'framer-motion';
    import { cn } from '@/lib/utils';
    import { navLinks } from '@/lib/data';
    import { Building2, ChevronRight, User, LogOut } from 'lucide-react';
    import { useAuth } from '@/contexts/AuthContext';
    import { Button } from '@/components/ui/button';

    const Sidebar = () => {
      const { user, logout } = useAuth();
      const [hoveredItem, setHoveredItem] = useState(null);

      const filteredNavLinks = navLinks.filter(link => {
        if (!link.roles || user?.role === 'Admin') {
          return true;
        }
        return link.roles.includes(user?.role);
      });

      const handleLogout = () => {
        logout();
      };

      return (
        <motion.aside
          initial={{ x: -300, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
          className="hidden md:flex flex-col w-64 bg-gradient-to-b from-blue-900 via-blue-800 to-blue-900 h-full shadow-2xl relative overflow-hidden"
        >
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-400 to-transparent"></div>
            <div className="absolute bottom-0 right-0 w-32 h-32 bg-blue-300 rounded-full blur-3xl opacity-20"></div>
            <div className="absolute top-1/3 left-0 w-24 h-24 bg-blue-400 rounded-full blur-2xl opacity-15"></div>
          </div>

          {/* Header */}
          <motion.div
            initial={{ y: -50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            className="relative z-10 p-6 flex items-center justify-center space-x-3 border-b border-blue-700/30"
          >
            <motion.div
              whileHover={{ rotate: 360, scale: 1.1 }}
              transition={{ duration: 0.6 }}
            >
              <Building2 className="h-9 w-9 text-blue-200" />
            </motion.div>
            <span className="font-bold text-xl tracking-tight text-white">Beyond Smart Glass</span>
          </motion.div>

          {/* User Info */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="relative z-10 p-4 mx-4 mt-4 bg-blue-800/50 rounded-xl border border-blue-600/30 backdrop-blur-sm"
          >
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">{user?.name}</p>
                <p className="text-xs text-blue-200 truncate">{user?.role}</p>
              </div>
            </div>
          </motion.div>

          {/* Navigation */}
          <nav className="relative z-10 flex-1 px-4 py-6">
            <ul className="space-y-2">
              {filteredNavLinks.map((link, index) => (
                <motion.li
                  key={link.label}
                  initial={{ x: -50, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.4 + index * 0.1, duration: 0.5 }}
                >
                  <NavLink to={link.href}>
                    {({ isActive }) => (
                      <motion.div
                        className={cn(
                          'relative flex items-center gap-3 rounded-xl px-4 py-3 text-blue-100 transition-all duration-300 font-medium group cursor-pointer overflow-hidden',
                          isActive
                            ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                            : 'hover:bg-blue-700/50 hover:text-white'
                        )}
                        whileHover={{
                          scale: 1.02,
                          x: 5,
                          transition: { duration: 0.2 }
                        }}
                        whileTap={{ scale: 0.98 }}
                        onHoverStart={() => setHoveredItem(link.label)}
                        onHoverEnd={() => setHoveredItem(null)}
                      >
                        {/* Background Glow Effect */}
                        <AnimatePresence>
                          {(isActive || hoveredItem === link.label) && (
                            <motion.div
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              exit={{ opacity: 0, scale: 0.8 }}
                              className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-blue-400/20 rounded-xl"
                            />
                          )}
                        </AnimatePresence>

                        {/* Icon */}
                        <motion.div
                          whileHover={{ rotate: 5, scale: 1.1 }}
                          transition={{ duration: 0.2 }}
                          className="relative z-10"
                        >
                          <link.icon className={cn(
                            "h-5 w-5 transition-colors duration-300",
                            isActive ? "text-white" : "text-blue-200 group-hover:text-white"
                          )} />
                        </motion.div>

                        {/* Label */}
                        <span className="relative z-10 transition-colors duration-300">
                          {link.label}
                        </span>

                        {/* Active Indicator */}
                        {isActive && (
                          <motion.div
                            layoutId="active-indicator"
                            className="absolute right-3 top-1/2 transform -translate-y-1/2"
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ duration: 0.3 }}
                          >
                            <ChevronRight className="h-4 w-4 text-white" />
                          </motion.div>
                        )}

                        {/* Hover Arrow */}
                        <AnimatePresence>
                          {hoveredItem === link.label && !isActive && (
                            <motion.div
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              exit={{ opacity: 0, x: -10 }}
                              className="absolute right-3 top-1/2 transform -translate-y-1/2"
                            >
                              <ChevronRight className="h-4 w-4 text-blue-200" />
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </motion.div>
                    )}
                  </NavLink>
                </motion.li>
              ))}
            </ul>
          </nav>

          {/* Logout Button */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="relative z-10 p-4 border-t border-blue-700/30"
          >
            <Button
              onClick={handleLogout}
              variant="ghost"
              className="w-full justify-start gap-3 text-blue-200 hover:text-white hover:bg-blue-700/50 transition-all duration-300"
            >
              <LogOut className="h-5 w-5" />
              تسجيل الخروج
            </Button>
          </motion.div>
        </motion.aside>
      );
    };

    export default Sidebar;
