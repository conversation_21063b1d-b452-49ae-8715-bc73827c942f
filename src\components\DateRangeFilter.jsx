import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Filter } from 'lucide-react';
import { motion } from 'framer-motion';

const DateRangeFilter = ({ onDateRangeChange, className = "" }) => {
  const [filterType, setFilterType] = useState('week');
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);

  // حساب التواريخ بناءً على النوع المحدد
  const calculateDateRange = (type) => {
    const now = new Date();
    let startDate, endDate;

    switch (type) {
      case 'week':
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay());
        startOfWeek.setHours(0, 0, 0, 0);
        
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        endOfWeek.setHours(23, 59, 59, 999);
        
        startDate = startOfWeek;
        endDate = endOfWeek;
        break;

      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
        break;

      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        endDate = new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999);
        break;

      case 'custom':
        if (customStartDate && customEndDate) {
          startDate = new Date(customStartDate);
          endDate = new Date(customEndDate);
          endDate.setHours(23, 59, 59, 999);
        }
        break;

      default:
        return null;
    }

    return { startDate, endDate };
  };

  // تطبيق الفلتر
  const applyFilter = () => {
    const dateRange = calculateDateRange(filterType);
    if (dateRange && onDateRangeChange) {
      onDateRangeChange(dateRange);
    }
  };

  // تغيير نوع الفلتر
  const handleFilterTypeChange = (type) => {
    setFilterType(type);
    if (type !== 'custom') {
      const dateRange = calculateDateRange(type);
      if (dateRange && onDateRangeChange) {
        onDateRangeChange(dateRange);
      }
    }
  };

  // تنسيق التاريخ للعرض
  const formatDate = (date) => {
    return date ? date.toLocaleDateString('ar-SA') : '';
  };

  // الحصول على النطاق الحالي للعرض
  const getCurrentRange = () => {
    const range = calculateDateRange(filterType);
    if (!range) return '';
    
    return `${formatDate(range.startDate)} - ${formatDate(range.endDate)}`;
  };

  return (
    <Card className={`${className} border-blue-200 shadow-lg`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-blue-700">
          <Calendar className="h-5 w-5" />
          Date Filter
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* خيارات الفلتر السريع */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {[
            { value: 'week', label: 'This Week', icon: '📅' },
            { value: 'month', label: 'This Month', icon: '🗓️' },
            { value: 'year', label: 'This Year', icon: '📆' },
            { value: 'custom', label: 'Custom', icon: '⚙️' }
          ].map((option) => (
            <motion.div key={option.value} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                variant={filterType === option.value ? "default" : "outline"}
                size="sm"
                onClick={() => handleFilterTypeChange(option.value)}
                className={`w-full h-auto p-3 flex flex-col items-center gap-1 ${
                  filterType === option.value 
                    ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                    : 'hover:bg-blue-50 hover:border-blue-300'
                }`}
              >
                <span className="text-lg">{option.icon}</span>
                <span className="text-xs font-medium">{option.label}</span>
              </Button>
            </motion.div>
          ))}
        </div>

        {/* النطاق الحالي */}
        {filterType !== 'custom' && (
          <motion.div 
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-blue-50 p-3 rounded-lg border border-blue-200"
          >
            <div className="text-sm text-blue-700 font-medium">Selected Range:</div>
            <div className="text-blue-900 font-semibold">{getCurrentRange()}</div>
          </motion.div>
        )}

        {/* التاريخ المخصص */}
        {filterType === 'custom' && (
          <motion.div 
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-3 bg-gray-50 p-4 rounded-lg border"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor="startDate" className="text-sm font-medium text-gray-700">
                  From Date
                </Label>
                <Input
                  id="startDate"
                  type="date"
                  value={customStartDate}
                  onChange={(e) => setCustomStartDate(e.target.value)}
                  className="border-gray-300 focus:border-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endDate" className="text-sm font-medium text-gray-700">
                  To Date
                </Label>
                <Input
                  id="endDate"
                  type="date"
                  value={customEndDate}
                  onChange={(e) => setCustomEndDate(e.target.value)}
                  className="border-gray-300 focus:border-blue-500"
                />
              </div>
            </div>
            <Button 
              onClick={applyFilter}
              disabled={!customStartDate || !customEndDate}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              <Filter className="h-4 w-4 mr-2" />
              Apply Filter
            </Button>
          </motion.div>
        )}

        {/* إحصائيات سريعة */}
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-3 gap-2 pt-2 border-t border-gray-200"
        >
          <div className="text-center p-2 bg-green-50 rounded-lg">
            <div className="text-xs text-green-600">Today</div>
            <div className="text-sm font-bold text-green-700">
              {new Date().toLocaleDateString('en-US', { day: 'numeric' })}
            </div>
          </div>
          <div className="text-center p-2 bg-blue-50 rounded-lg">
            <div className="text-xs text-blue-600">Month</div>
            <div className="text-sm font-bold text-blue-700">
              {new Date().toLocaleDateString('en-US', { month: 'short' })}
            </div>
          </div>
          <div className="text-center p-2 bg-purple-50 rounded-lg">
            <div className="text-xs text-purple-600">Year</div>
            <div className="text-sm font-bold text-purple-700">
              {new Date().getFullYear()}
            </div>
          </div>
        </motion.div>
      </CardContent>
    </Card>
  );
};

export default DateRangeFilter;
