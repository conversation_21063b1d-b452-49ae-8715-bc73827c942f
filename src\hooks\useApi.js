import { useState, useEffect } from 'react';
import apiService from '@/services/api';

// Custom hook for API data fetching and management
export const useApi = (endpoint, dependencies = []) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let result;
      switch (endpoint) {
        case 'clients':
          result = await apiService.getClients();
          break;
        case 'quotations':
          result = await apiService.getQuotations();
          break;
        case 'projects':
          result = await apiService.getProjects();
          break;
        case 'inventory':
          result = await apiService.getInventoryItems();
          break;
        case 'maintenance':
          result = await apiService.getMaintenanceRequests();
          break;
        case 'users':
          result = await apiService.getUsers();
          break;
        default:
          throw new Error(`Unknown endpoint: ${endpoint}`);
      }
      
      setData(result);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, dependencies);

  const refetch = () => {
    fetchData();
  };

  return { data, loading, error, refetch };
};

// Hook for clients
export const useClients = () => {
  const { data, loading, error, refetch } = useApi('clients');

  const createClient = async (clientData) => {
    try {
      const newClient = await apiService.createClient(clientData);
      refetch();
      return newClient;
    } catch (err) {
      throw err;
    }
  };

  const updateClient = async (id, clientData) => {
    try {
      const updatedClient = await apiService.updateClient(id, clientData);
      refetch();
      return updatedClient;
    } catch (err) {
      throw err;
    }
  };

  const deleteClient = async (id) => {
    try {
      await apiService.deleteClient(id);
      refetch();
    } catch (err) {
      throw err;
    }
  };

  return {
    clients: data || [],
    loading,
    error,
    refetch,
    createClient,
    updateClient,
    deleteClient,
  };
};

// Hook for quotations
export const useQuotations = () => {
  const { data, loading, error, refetch } = useApi('quotations');

  const createQuotation = async (quotationData) => {
    try {
      const newQuotation = await apiService.createQuotation(quotationData);
      refetch();
      return newQuotation;
    } catch (err) {
      throw err;
    }
  };

  const updateQuotation = async (id, quotationData) => {
    try {
      const updatedQuotation = await apiService.updateQuotation(id, quotationData);
      refetch();
      return updatedQuotation;
    } catch (err) {
      throw err;
    }
  };

  const deleteQuotation = async (id) => {
    try {
      await apiService.deleteQuotation(id);
      refetch();
    } catch (err) {
      throw err;
    }
  };

  return {
    quotations: data || [],
    loading,
    error,
    refetch,
    createQuotation,
    updateQuotation,
    deleteQuotation,
  };
};

// Hook for projects
export const useProjects = () => {
  const { data, loading, error, refetch } = useApi('projects');

  const createProject = async (projectData) => {
    try {
      const newProject = await apiService.createProject(projectData);
      refetch();
      return newProject;
    } catch (err) {
      throw err;
    }
  };

  const updateProject = async (id, projectData) => {
    try {
      const updatedProject = await apiService.updateProject(id, projectData);
      refetch();
      return updatedProject;
    } catch (err) {
      throw err;
    }
  };

  const deleteProject = async (id) => {
    try {
      await apiService.deleteProject(id);
      refetch();
    } catch (err) {
      throw err;
    }
  };

  return {
    projects: data || [],
    loading,
    error,
    refetch,
    createProject,
    updateProject,
    deleteProject,
  };
};

// Hook for inventory
export const useInventory = () => {
  const { data, loading, error, refetch } = useApi('inventory');

  const createInventoryItem = async (itemData) => {
    try {
      const newItem = await apiService.createInventoryItem(itemData);
      refetch();
      return newItem;
    } catch (err) {
      throw err;
    }
  };

  const updateInventoryItem = async (id, itemData) => {
    try {
      const updatedItem = await apiService.updateInventoryItem(id, itemData);
      refetch();
      return updatedItem;
    } catch (err) {
      throw err;
    }
  };

  const deleteInventoryItem = async (id) => {
    try {
      await apiService.deleteInventoryItem(id);
      refetch();
    } catch (err) {
      throw err;
    }
  };

  return {
    inventory: data || [],
    loading,
    error,
    refetch,
    createInventoryItem,
    updateInventoryItem,
    deleteInventoryItem,
  };
};

// Hook for maintenance requests
export const useMaintenance = () => {
  const { data, loading, error, refetch } = useApi('maintenance');

  const createMaintenanceRequest = async (requestData) => {
    try {
      const newRequest = await apiService.createMaintenanceRequest(requestData);
      refetch();
      return newRequest;
    } catch (err) {
      throw err;
    }
  };

  const updateMaintenanceRequest = async (id, requestData) => {
    try {
      const updatedRequest = await apiService.updateMaintenanceRequest(id, requestData);
      refetch();
      return updatedRequest;
    } catch (err) {
      throw err;
    }
  };

  const deleteMaintenanceRequest = async (id) => {
    try {
      await apiService.deleteMaintenanceRequest(id);
      refetch();
    } catch (err) {
      throw err;
    }
  };

  return {
    maintenanceRequests: data || [],
    loading,
    error,
    refetch,
    createMaintenanceRequest,
    updateMaintenanceRequest,
    deleteMaintenanceRequest,
  };
};

// Hook for users (Admin only)
export const useUsers = () => {
  const { data, loading, error, refetch } = useApi('users');

  const updateUser = async (id, userData) => {
    try {
      const updatedUser = await apiService.updateUser(id, userData);
      refetch();
      return updatedUser;
    } catch (err) {
      throw err;
    }
  };

  const deleteUser = async (id) => {
    try {
      await apiService.deleteUser(id);
      refetch();
    } catch (err) {
      throw err;
    }
  };

  return {
    users: data || [],
    loading,
    error,
    refetch,
    updateUser,
    deleteUser,
  };
};
