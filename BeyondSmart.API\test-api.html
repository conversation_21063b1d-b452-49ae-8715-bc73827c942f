<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار BeyondSmart API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        input {
            width: 200px;
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 اختبار BeyondSmart API</h1>
        
        <div class="section">
            <h2>🔐 تسجيل الدخول</h2>
            <input type="email" id="email" placeholder="البريد الإلكتروني" value="<EMAIL>">
            <input type="password" id="password" placeholder="كلمة المرور" value="admin123">
            <button onclick="login()">تسجيل الدخول</button>
            <div id="loginResult" class="result"></div>
        </div>

        <div class="section">
            <h2>👥 العملاء</h2>
            <button onclick="getClients()">جلب العملاء</button>
            <button onclick="addClient()">إضافة عميل تجريبي</button>
            <div id="clientsResult" class="result"></div>
        </div>

        <div class="section">
            <h2>📦 المخزون</h2>
            <button onclick="getInventory()">جلب المخزون</button>
            <div id="inventoryResult" class="result"></div>
        </div>

        <div class="section">
            <h2>👤 المستخدمين</h2>
            <button onclick="getUsers()">جلب المستخدمين</button>
            <div id="usersResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5181/api';
        let authToken = '';

        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.token;
                    document.getElementById('loginResult').innerHTML = 
                        `✅ تم تسجيل الدخول بنجاح!\nالمستخدم: ${data.user.name}\nالدور: ${data.user.role}\nالتوكن: ${data.token.substring(0, 50)}...`;
                    document.getElementById('loginResult').className = 'result success';
                } else {
                    document.getElementById('loginResult').innerHTML = `❌ خطأ: ${data.message || 'فشل تسجيل الدخول'}`;
                    document.getElementById('loginResult').className = 'result error';
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = `❌ خطأ في الاتصال: ${error.message}`;
                document.getElementById('loginResult').className = 'result error';
            }
        }

        async function getClients() {
            try {
                const response = await fetch(`${API_BASE}/clients`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('clientsResult').innerHTML = 
                        `✅ تم جلب العملاء بنجاح!\nعدد العملاء: ${data.length}\n\n${JSON.stringify(data, null, 2)}`;
                    document.getElementById('clientsResult').className = 'result success';
                } else {
                    document.getElementById('clientsResult').innerHTML = `❌ خطأ: ${data.message || 'فشل جلب العملاء'}`;
                    document.getElementById('clientsResult').className = 'result error';
                }
            } catch (error) {
                document.getElementById('clientsResult').innerHTML = `❌ خطأ في الاتصال: ${error.message}`;
                document.getElementById('clientsResult').className = 'result error';
            }
        }

        async function addClient() {
            const newClient = {
                name: "عميل تجريبي",
                phone: "+966501234567",
                email: "<EMAIL>",
                city: "الرياض"
            };

            try {
                const response = await fetch(`${API_BASE}/clients`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(newClient)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('clientsResult').innerHTML = 
                        `✅ تم إضافة العميل بنجاح!\n\n${JSON.stringify(data, null, 2)}`;
                    document.getElementById('clientsResult').className = 'result success';
                } else {
                    document.getElementById('clientsResult').innerHTML = `❌ خطأ: ${data.message || 'فشل إضافة العميل'}`;
                    document.getElementById('clientsResult').className = 'result error';
                }
            } catch (error) {
                document.getElementById('clientsResult').innerHTML = `❌ خطأ في الاتصال: ${error.message}`;
                document.getElementById('clientsResult').className = 'result error';
            }
        }

        async function getInventory() {
            try {
                const response = await fetch(`${API_BASE}/inventoryitems`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('inventoryResult').innerHTML = 
                        `✅ تم جلب المخزون بنجاح!\nعدد العناصر: ${data.length}\n\n${JSON.stringify(data, null, 2)}`;
                    document.getElementById('inventoryResult').className = 'result success';
                } else {
                    document.getElementById('inventoryResult').innerHTML = `❌ خطأ: ${data.message || 'فشل جلب المخزون'}`;
                    document.getElementById('inventoryResult').className = 'result error';
                }
            } catch (error) {
                document.getElementById('inventoryResult').innerHTML = `❌ خطأ في الاتصال: ${error.message}`;
                document.getElementById('inventoryResult').className = 'result error';
            }
        }

        async function getUsers() {
            try {
                const response = await fetch(`${API_BASE}/users`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('usersResult').innerHTML = 
                        `✅ تم جلب المستخدمين بنجاح!\nعدد المستخدمين: ${data.length}\n\n${JSON.stringify(data, null, 2)}`;
                    document.getElementById('usersResult').className = 'result success';
                } else {
                    document.getElementById('usersResult').innerHTML = `❌ خطأ: ${data.message || 'فشل جلب المستخدمين'}`;
                    document.getElementById('usersResult').className = 'result error';
                }
            } catch (error) {
                document.getElementById('usersResult').innerHTML = `❌ خطأ في الاتصال: ${error.message}`;
                document.getElementById('usersResult').className = 'result error';
            }
        }
    </script>
</body>
</html>
