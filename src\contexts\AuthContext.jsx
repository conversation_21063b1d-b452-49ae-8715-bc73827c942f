import React, { createContext, useContext, useState, useEffect } from 'react';
    import apiService from '@/services/api';

    const AuthContext = createContext();

    export const useAuth = () => {
      return useContext(AuthContext);
    };

    export const AuthProvider = ({ children }) => {
      const [user, setUser] = useState(null);
      const [loading, setLoading] = useState(true);
      const [error, setError] = useState(null);

      useEffect(() => {
        const loggedInUser = localStorage.getItem('bst-auth-user');
        const token = localStorage.getItem('bst-auth-token');

        if (loggedInUser && token) {
          setUser(JSON.parse(loggedInUser));
          apiService.setToken(token);
        }
        setLoading(false);
      }, []);

      const login = async (email, password) => {
        try {
          setLoading(true);
          setError(null);

          const response = await apiService.login(email, password);

          setUser(response.user);
          localStorage.setItem('bst-auth-user', JSON.stringify(response.user));

          return response.user;
        } catch (err) {
          setError(err.message);
          throw err;
        } finally {
          setLoading(false);
        }
      };

      const logout = () => {
        setUser(null);
        apiService.clearToken();
        localStorage.removeItem('bst-auth-user');
      };

      const register = async (userData) => {
        try {
          setLoading(true);
          setError(null);

          const response = await apiService.register(userData);
          return response;
        } catch (err) {
          setError(err.message);
          throw err;
        } finally {
          setLoading(false);
        }
      };

      const value = {
        user,
        login,
        logout,
        register,
        isAuthenticated: !!user,
        loading,
        error,
        setError,
      };

      return (
        <AuthContext.Provider value={value}>
          {!loading && children}
        </AuthContext.Provider>
      );
    };