{"name": "beyond-smart-tech-erp", "type": "module", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.1", "lucide-react": "^0.292.0", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-day-picker": "^8.9.1", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-router-dom": "^6.16.0", "recharts": "^2.9.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^20.8.3", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "eslint": "^8.57.1", "eslint-config-react-app": "^7.0.1", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^4.4.5"}}