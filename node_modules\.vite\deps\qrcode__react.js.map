{"version": 3, "sources": ["../../qrcode.react/lib/esm/index.js"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/index.tsx\nimport React from \"react\";\n\n// src/third-party/qrcodegen/index.ts\n/**\n * @license QR Code generator library (TypeScript)\n * Copyright (c) Project Nayuki.\n * SPDX-License-Identifier: MIT\n */\nvar qrcodegen;\n((qrcodegen2) => {\n  const _QrCode = class {\n    constructor(version, errorCorrectionLevel, dataCodewords, msk) {\n      this.version = version;\n      this.errorCorrectionLevel = errorCorrectionLevel;\n      this.modules = [];\n      this.isFunction = [];\n      if (version < _QrCode.MIN_VERSION || version > _QrCode.MAX_VERSION)\n        throw new RangeError(\"Version value out of range\");\n      if (msk < -1 || msk > 7)\n        throw new RangeError(\"Mask value out of range\");\n      this.size = version * 4 + 17;\n      let row = [];\n      for (let i = 0; i < this.size; i++)\n        row.push(false);\n      for (let i = 0; i < this.size; i++) {\n        this.modules.push(row.slice());\n        this.isFunction.push(row.slice());\n      }\n      this.drawFunctionPatterns();\n      const allCodewords = this.addEccAndInterleave(dataCodewords);\n      this.drawCodewords(allCodewords);\n      if (msk == -1) {\n        let minPenalty = 1e9;\n        for (let i = 0; i < 8; i++) {\n          this.applyMask(i);\n          this.drawFormatBits(i);\n          const penalty = this.getPenaltyScore();\n          if (penalty < minPenalty) {\n            msk = i;\n            minPenalty = penalty;\n          }\n          this.applyMask(i);\n        }\n      }\n      assert(0 <= msk && msk <= 7);\n      this.mask = msk;\n      this.applyMask(msk);\n      this.drawFormatBits(msk);\n      this.isFunction = [];\n    }\n    static encodeText(text, ecl) {\n      const segs = qrcodegen2.QrSegment.makeSegments(text);\n      return _QrCode.encodeSegments(segs, ecl);\n    }\n    static encodeBinary(data, ecl) {\n      const seg = qrcodegen2.QrSegment.makeBytes(data);\n      return _QrCode.encodeSegments([seg], ecl);\n    }\n    static encodeSegments(segs, ecl, minVersion = 1, maxVersion = 40, mask = -1, boostEcl = true) {\n      if (!(_QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= _QrCode.MAX_VERSION) || mask < -1 || mask > 7)\n        throw new RangeError(\"Invalid value\");\n      let version;\n      let dataUsedBits;\n      for (version = minVersion; ; version++) {\n        const dataCapacityBits2 = _QrCode.getNumDataCodewords(version, ecl) * 8;\n        const usedBits = QrSegment.getTotalBits(segs, version);\n        if (usedBits <= dataCapacityBits2) {\n          dataUsedBits = usedBits;\n          break;\n        }\n        if (version >= maxVersion)\n          throw new RangeError(\"Data too long\");\n      }\n      for (const newEcl of [_QrCode.Ecc.MEDIUM, _QrCode.Ecc.QUARTILE, _QrCode.Ecc.HIGH]) {\n        if (boostEcl && dataUsedBits <= _QrCode.getNumDataCodewords(version, newEcl) * 8)\n          ecl = newEcl;\n      }\n      let bb = [];\n      for (const seg of segs) {\n        appendBits(seg.mode.modeBits, 4, bb);\n        appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n        for (const b of seg.getData())\n          bb.push(b);\n      }\n      assert(bb.length == dataUsedBits);\n      const dataCapacityBits = _QrCode.getNumDataCodewords(version, ecl) * 8;\n      assert(bb.length <= dataCapacityBits);\n      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n      appendBits(0, (8 - bb.length % 8) % 8, bb);\n      assert(bb.length % 8 == 0);\n      for (let padByte = 236; bb.length < dataCapacityBits; padByte ^= 236 ^ 17)\n        appendBits(padByte, 8, bb);\n      let dataCodewords = [];\n      while (dataCodewords.length * 8 < bb.length)\n        dataCodewords.push(0);\n      bb.forEach((b, i) => dataCodewords[i >>> 3] |= b << 7 - (i & 7));\n      return new _QrCode(version, ecl, dataCodewords, mask);\n    }\n    getModule(x, y) {\n      return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];\n    }\n    getModules() {\n      return this.modules;\n    }\n    drawFunctionPatterns() {\n      for (let i = 0; i < this.size; i++) {\n        this.setFunctionModule(6, i, i % 2 == 0);\n        this.setFunctionModule(i, 6, i % 2 == 0);\n      }\n      this.drawFinderPattern(3, 3);\n      this.drawFinderPattern(this.size - 4, 3);\n      this.drawFinderPattern(3, this.size - 4);\n      const alignPatPos = this.getAlignmentPatternPositions();\n      const numAlign = alignPatPos.length;\n      for (let i = 0; i < numAlign; i++) {\n        for (let j = 0; j < numAlign; j++) {\n          if (!(i == 0 && j == 0 || i == 0 && j == numAlign - 1 || i == numAlign - 1 && j == 0))\n            this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n        }\n      }\n      this.drawFormatBits(0);\n      this.drawVersion();\n    }\n    drawFormatBits(mask) {\n      const data = this.errorCorrectionLevel.formatBits << 3 | mask;\n      let rem = data;\n      for (let i = 0; i < 10; i++)\n        rem = rem << 1 ^ (rem >>> 9) * 1335;\n      const bits = (data << 10 | rem) ^ 21522;\n      assert(bits >>> 15 == 0);\n      for (let i = 0; i <= 5; i++)\n        this.setFunctionModule(8, i, getBit(bits, i));\n      this.setFunctionModule(8, 7, getBit(bits, 6));\n      this.setFunctionModule(8, 8, getBit(bits, 7));\n      this.setFunctionModule(7, 8, getBit(bits, 8));\n      for (let i = 9; i < 15; i++)\n        this.setFunctionModule(14 - i, 8, getBit(bits, i));\n      for (let i = 0; i < 8; i++)\n        this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n      for (let i = 8; i < 15; i++)\n        this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n      this.setFunctionModule(8, this.size - 8, true);\n    }\n    drawVersion() {\n      if (this.version < 7)\n        return;\n      let rem = this.version;\n      for (let i = 0; i < 12; i++)\n        rem = rem << 1 ^ (rem >>> 11) * 7973;\n      const bits = this.version << 12 | rem;\n      assert(bits >>> 18 == 0);\n      for (let i = 0; i < 18; i++) {\n        const color = getBit(bits, i);\n        const a = this.size - 11 + i % 3;\n        const b = Math.floor(i / 3);\n        this.setFunctionModule(a, b, color);\n        this.setFunctionModule(b, a, color);\n      }\n    }\n    drawFinderPattern(x, y) {\n      for (let dy = -4; dy <= 4; dy++) {\n        for (let dx = -4; dx <= 4; dx++) {\n          const dist = Math.max(Math.abs(dx), Math.abs(dy));\n          const xx = x + dx;\n          const yy = y + dy;\n          if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size)\n            this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n        }\n      }\n    }\n    drawAlignmentPattern(x, y) {\n      for (let dy = -2; dy <= 2; dy++) {\n        for (let dx = -2; dx <= 2; dx++)\n          this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n      }\n    }\n    setFunctionModule(x, y, isDark) {\n      this.modules[y][x] = isDark;\n      this.isFunction[y][x] = true;\n    }\n    addEccAndInterleave(data) {\n      const ver = this.version;\n      const ecl = this.errorCorrectionLevel;\n      if (data.length != _QrCode.getNumDataCodewords(ver, ecl))\n        throw new RangeError(\"Invalid argument\");\n      const numBlocks = _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n      const blockEccLen = _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n      const rawCodewords = Math.floor(_QrCode.getNumRawDataModules(ver) / 8);\n      const numShortBlocks = numBlocks - rawCodewords % numBlocks;\n      const shortBlockLen = Math.floor(rawCodewords / numBlocks);\n      let blocks = [];\n      const rsDiv = _QrCode.reedSolomonComputeDivisor(blockEccLen);\n      for (let i = 0, k = 0; i < numBlocks; i++) {\n        let dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n        k += dat.length;\n        const ecc = _QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n        if (i < numShortBlocks)\n          dat.push(0);\n        blocks.push(dat.concat(ecc));\n      }\n      let result = [];\n      for (let i = 0; i < blocks[0].length; i++) {\n        blocks.forEach((block, j) => {\n          if (i != shortBlockLen - blockEccLen || j >= numShortBlocks)\n            result.push(block[i]);\n        });\n      }\n      assert(result.length == rawCodewords);\n      return result;\n    }\n    drawCodewords(data) {\n      if (data.length != Math.floor(_QrCode.getNumRawDataModules(this.version) / 8))\n        throw new RangeError(\"Invalid argument\");\n      let i = 0;\n      for (let right = this.size - 1; right >= 1; right -= 2) {\n        if (right == 6)\n          right = 5;\n        for (let vert = 0; vert < this.size; vert++) {\n          for (let j = 0; j < 2; j++) {\n            const x = right - j;\n            const upward = (right + 1 & 2) == 0;\n            const y = upward ? this.size - 1 - vert : vert;\n            if (!this.isFunction[y][x] && i < data.length * 8) {\n              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n              i++;\n            }\n          }\n        }\n      }\n      assert(i == data.length * 8);\n    }\n    applyMask(mask) {\n      if (mask < 0 || mask > 7)\n        throw new RangeError(\"Mask value out of range\");\n      for (let y = 0; y < this.size; y++) {\n        for (let x = 0; x < this.size; x++) {\n          let invert;\n          switch (mask) {\n            case 0:\n              invert = (x + y) % 2 == 0;\n              break;\n            case 1:\n              invert = y % 2 == 0;\n              break;\n            case 2:\n              invert = x % 3 == 0;\n              break;\n            case 3:\n              invert = (x + y) % 3 == 0;\n              break;\n            case 4:\n              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n              break;\n            case 5:\n              invert = x * y % 2 + x * y % 3 == 0;\n              break;\n            case 6:\n              invert = (x * y % 2 + x * y % 3) % 2 == 0;\n              break;\n            case 7:\n              invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n              break;\n            default:\n              throw new Error(\"Unreachable\");\n          }\n          if (!this.isFunction[y][x] && invert)\n            this.modules[y][x] = !this.modules[y][x];\n        }\n      }\n    }\n    getPenaltyScore() {\n      let result = 0;\n      for (let y = 0; y < this.size; y++) {\n        let runColor = false;\n        let runX = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let x = 0; x < this.size; x++) {\n          if (this.modules[y][x] == runColor) {\n            runX++;\n            if (runX == 5)\n              result += _QrCode.PENALTY_N1;\n            else if (runX > 5)\n              result++;\n          } else {\n            this.finderPenaltyAddHistory(runX, runHistory);\n            if (!runColor)\n              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runX = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let x = 0; x < this.size; x++) {\n        let runColor = false;\n        let runY = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let y = 0; y < this.size; y++) {\n          if (this.modules[y][x] == runColor) {\n            runY++;\n            if (runY == 5)\n              result += _QrCode.PENALTY_N1;\n            else if (runY > 5)\n              result++;\n          } else {\n            this.finderPenaltyAddHistory(runY, runHistory);\n            if (!runColor)\n              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runY = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let y = 0; y < this.size - 1; y++) {\n        for (let x = 0; x < this.size - 1; x++) {\n          const color = this.modules[y][x];\n          if (color == this.modules[y][x + 1] && color == this.modules[y + 1][x] && color == this.modules[y + 1][x + 1])\n            result += _QrCode.PENALTY_N2;\n        }\n      }\n      let dark = 0;\n      for (const row of this.modules)\n        dark = row.reduce((sum, color) => sum + (color ? 1 : 0), dark);\n      const total = this.size * this.size;\n      const k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n      assert(0 <= k && k <= 9);\n      result += k * _QrCode.PENALTY_N4;\n      assert(0 <= result && result <= 2568888);\n      return result;\n    }\n    getAlignmentPatternPositions() {\n      if (this.version == 1)\n        return [];\n      else {\n        const numAlign = Math.floor(this.version / 7) + 2;\n        const step = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n        let result = [6];\n        for (let pos = this.size - 7; result.length < numAlign; pos -= step)\n          result.splice(1, 0, pos);\n        return result;\n      }\n    }\n    static getNumRawDataModules(ver) {\n      if (ver < _QrCode.MIN_VERSION || ver > _QrCode.MAX_VERSION)\n        throw new RangeError(\"Version number out of range\");\n      let result = (16 * ver + 128) * ver + 64;\n      if (ver >= 2) {\n        const numAlign = Math.floor(ver / 7) + 2;\n        result -= (25 * numAlign - 10) * numAlign - 55;\n        if (ver >= 7)\n          result -= 36;\n      }\n      assert(208 <= result && result <= 29648);\n      return result;\n    }\n    static getNumDataCodewords(ver, ecl) {\n      return Math.floor(_QrCode.getNumRawDataModules(ver) / 8) - _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n    }\n    static reedSolomonComputeDivisor(degree) {\n      if (degree < 1 || degree > 255)\n        throw new RangeError(\"Degree out of range\");\n      let result = [];\n      for (let i = 0; i < degree - 1; i++)\n        result.push(0);\n      result.push(1);\n      let root = 1;\n      for (let i = 0; i < degree; i++) {\n        for (let j = 0; j < result.length; j++) {\n          result[j] = _QrCode.reedSolomonMultiply(result[j], root);\n          if (j + 1 < result.length)\n            result[j] ^= result[j + 1];\n        }\n        root = _QrCode.reedSolomonMultiply(root, 2);\n      }\n      return result;\n    }\n    static reedSolomonComputeRemainder(data, divisor) {\n      let result = divisor.map((_) => 0);\n      for (const b of data) {\n        const factor = b ^ result.shift();\n        result.push(0);\n        divisor.forEach((coef, i) => result[i] ^= _QrCode.reedSolomonMultiply(coef, factor));\n      }\n      return result;\n    }\n    static reedSolomonMultiply(x, y) {\n      if (x >>> 8 != 0 || y >>> 8 != 0)\n        throw new RangeError(\"Byte out of range\");\n      let z = 0;\n      for (let i = 7; i >= 0; i--) {\n        z = z << 1 ^ (z >>> 7) * 285;\n        z ^= (y >>> i & 1) * x;\n      }\n      assert(z >>> 8 == 0);\n      return z;\n    }\n    finderPenaltyCountPatterns(runHistory) {\n      const n = runHistory[1];\n      assert(n <= this.size * 3);\n      const core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n      return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n    }\n    finderPenaltyTerminateAndCount(currentRunColor, currentRunLength, runHistory) {\n      if (currentRunColor) {\n        this.finderPenaltyAddHistory(currentRunLength, runHistory);\n        currentRunLength = 0;\n      }\n      currentRunLength += this.size;\n      this.finderPenaltyAddHistory(currentRunLength, runHistory);\n      return this.finderPenaltyCountPatterns(runHistory);\n    }\n    finderPenaltyAddHistory(currentRunLength, runHistory) {\n      if (runHistory[0] == 0)\n        currentRunLength += this.size;\n      runHistory.pop();\n      runHistory.unshift(currentRunLength);\n    }\n  };\n  let QrCode = _QrCode;\n  QrCode.MIN_VERSION = 1;\n  QrCode.MAX_VERSION = 40;\n  QrCode.PENALTY_N1 = 3;\n  QrCode.PENALTY_N2 = 3;\n  QrCode.PENALTY_N3 = 40;\n  QrCode.PENALTY_N4 = 10;\n  QrCode.ECC_CODEWORDS_PER_BLOCK = [\n    [-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n    [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30]\n  ];\n  QrCode.NUM_ERROR_CORRECTION_BLOCKS = [\n    [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n    [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n    [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n    [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81]\n  ];\n  qrcodegen2.QrCode = QrCode;\n  function appendBits(val, len, bb) {\n    if (len < 0 || len > 31 || val >>> len != 0)\n      throw new RangeError(\"Value out of range\");\n    for (let i = len - 1; i >= 0; i--)\n      bb.push(val >>> i & 1);\n  }\n  function getBit(x, i) {\n    return (x >>> i & 1) != 0;\n  }\n  function assert(cond) {\n    if (!cond)\n      throw new Error(\"Assertion error\");\n  }\n  const _QrSegment = class {\n    constructor(mode, numChars, bitData) {\n      this.mode = mode;\n      this.numChars = numChars;\n      this.bitData = bitData;\n      if (numChars < 0)\n        throw new RangeError(\"Invalid argument\");\n      this.bitData = bitData.slice();\n    }\n    static makeBytes(data) {\n      let bb = [];\n      for (const b of data)\n        appendBits(b, 8, bb);\n      return new _QrSegment(_QrSegment.Mode.BYTE, data.length, bb);\n    }\n    static makeNumeric(digits) {\n      if (!_QrSegment.isNumeric(digits))\n        throw new RangeError(\"String contains non-numeric characters\");\n      let bb = [];\n      for (let i = 0; i < digits.length; ) {\n        const n = Math.min(digits.length - i, 3);\n        appendBits(parseInt(digits.substr(i, n), 10), n * 3 + 1, bb);\n        i += n;\n      }\n      return new _QrSegment(_QrSegment.Mode.NUMERIC, digits.length, bb);\n    }\n    static makeAlphanumeric(text) {\n      if (!_QrSegment.isAlphanumeric(text))\n        throw new RangeError(\"String contains unencodable characters in alphanumeric mode\");\n      let bb = [];\n      let i;\n      for (i = 0; i + 2 <= text.length; i += 2) {\n        let temp = _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n        temp += _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n        appendBits(temp, 11, bb);\n      }\n      if (i < text.length)\n        appendBits(_QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n      return new _QrSegment(_QrSegment.Mode.ALPHANUMERIC, text.length, bb);\n    }\n    static makeSegments(text) {\n      if (text == \"\")\n        return [];\n      else if (_QrSegment.isNumeric(text))\n        return [_QrSegment.makeNumeric(text)];\n      else if (_QrSegment.isAlphanumeric(text))\n        return [_QrSegment.makeAlphanumeric(text)];\n      else\n        return [_QrSegment.makeBytes(_QrSegment.toUtf8ByteArray(text))];\n    }\n    static makeEci(assignVal) {\n      let bb = [];\n      if (assignVal < 0)\n        throw new RangeError(\"ECI assignment value out of range\");\n      else if (assignVal < 1 << 7)\n        appendBits(assignVal, 8, bb);\n      else if (assignVal < 1 << 14) {\n        appendBits(2, 2, bb);\n        appendBits(assignVal, 14, bb);\n      } else if (assignVal < 1e6) {\n        appendBits(6, 3, bb);\n        appendBits(assignVal, 21, bb);\n      } else\n        throw new RangeError(\"ECI assignment value out of range\");\n      return new _QrSegment(_QrSegment.Mode.ECI, 0, bb);\n    }\n    static isNumeric(text) {\n      return _QrSegment.NUMERIC_REGEX.test(text);\n    }\n    static isAlphanumeric(text) {\n      return _QrSegment.ALPHANUMERIC_REGEX.test(text);\n    }\n    getData() {\n      return this.bitData.slice();\n    }\n    static getTotalBits(segs, version) {\n      let result = 0;\n      for (const seg of segs) {\n        const ccbits = seg.mode.numCharCountBits(version);\n        if (seg.numChars >= 1 << ccbits)\n          return Infinity;\n        result += 4 + ccbits + seg.bitData.length;\n      }\n      return result;\n    }\n    static toUtf8ByteArray(str) {\n      str = encodeURI(str);\n      let result = [];\n      for (let i = 0; i < str.length; i++) {\n        if (str.charAt(i) != \"%\")\n          result.push(str.charCodeAt(i));\n        else {\n          result.push(parseInt(str.substr(i + 1, 2), 16));\n          i += 2;\n        }\n      }\n      return result;\n    }\n  };\n  let QrSegment = _QrSegment;\n  QrSegment.NUMERIC_REGEX = /^[0-9]*$/;\n  QrSegment.ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+.\\/:-]*$/;\n  QrSegment.ALPHANUMERIC_CHARSET = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:\";\n  qrcodegen2.QrSegment = QrSegment;\n})(qrcodegen || (qrcodegen = {}));\n((qrcodegen2) => {\n  let QrCode;\n  ((QrCode2) => {\n    const _Ecc = class {\n      constructor(ordinal, formatBits) {\n        this.ordinal = ordinal;\n        this.formatBits = formatBits;\n      }\n    };\n    let Ecc = _Ecc;\n    Ecc.LOW = new _Ecc(0, 1);\n    Ecc.MEDIUM = new _Ecc(1, 0);\n    Ecc.QUARTILE = new _Ecc(2, 3);\n    Ecc.HIGH = new _Ecc(3, 2);\n    QrCode2.Ecc = Ecc;\n  })(QrCode = qrcodegen2.QrCode || (qrcodegen2.QrCode = {}));\n})(qrcodegen || (qrcodegen = {}));\n((qrcodegen2) => {\n  let QrSegment;\n  ((QrSegment2) => {\n    const _Mode = class {\n      constructor(modeBits, numBitsCharCount) {\n        this.modeBits = modeBits;\n        this.numBitsCharCount = numBitsCharCount;\n      }\n      numCharCountBits(ver) {\n        return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n      }\n    };\n    let Mode = _Mode;\n    Mode.NUMERIC = new _Mode(1, [10, 12, 14]);\n    Mode.ALPHANUMERIC = new _Mode(2, [9, 11, 13]);\n    Mode.BYTE = new _Mode(4, [8, 16, 16]);\n    Mode.KANJI = new _Mode(8, [8, 10, 12]);\n    Mode.ECI = new _Mode(7, [0, 0, 0]);\n    QrSegment2.Mode = Mode;\n  })(QrSegment = qrcodegen2.QrSegment || (qrcodegen2.QrSegment = {}));\n})(qrcodegen || (qrcodegen = {}));\nvar qrcodegen_default = qrcodegen;\n\n// src/index.tsx\n/**\n * @license qrcode.react\n * Copyright (c) Paul O'Shannessy\n * SPDX-License-Identifier: ISC\n */\nvar ERROR_LEVEL_MAP = {\n  L: qrcodegen_default.QrCode.Ecc.LOW,\n  M: qrcodegen_default.QrCode.Ecc.MEDIUM,\n  Q: qrcodegen_default.QrCode.Ecc.QUARTILE,\n  H: qrcodegen_default.QrCode.Ecc.HIGH\n};\nvar DEFAULT_SIZE = 128;\nvar DEFAULT_LEVEL = \"L\";\nvar DEFAULT_BGCOLOR = \"#FFFFFF\";\nvar DEFAULT_FGCOLOR = \"#000000\";\nvar DEFAULT_INCLUDEMARGIN = false;\nvar MARGIN_SIZE = 4;\nvar DEFAULT_IMG_SCALE = 0.1;\nfunction generatePath(modules, margin = 0) {\n  const ops = [];\n  modules.forEach(function(row, y) {\n    let start = null;\n    row.forEach(function(cell, x) {\n      if (!cell && start !== null) {\n        ops.push(`M${start + margin} ${y + margin}h${x - start}v1H${start + margin}z`);\n        start = null;\n        return;\n      }\n      if (x === row.length - 1) {\n        if (!cell) {\n          return;\n        }\n        if (start === null) {\n          ops.push(`M${x + margin},${y + margin} h1v1H${x + margin}z`);\n        } else {\n          ops.push(`M${start + margin},${y + margin} h${x + 1 - start}v1H${start + margin}z`);\n        }\n        return;\n      }\n      if (cell && start === null) {\n        start = x;\n      }\n    });\n  });\n  return ops.join(\"\");\n}\nfunction excavateModules(modules, excavation) {\n  return modules.slice().map((row, y) => {\n    if (y < excavation.y || y >= excavation.y + excavation.h) {\n      return row;\n    }\n    return row.map((cell, x) => {\n      if (x < excavation.x || x >= excavation.x + excavation.w) {\n        return cell;\n      }\n      return false;\n    });\n  });\n}\nfunction getImageSettings(cells, size, includeMargin, imageSettings) {\n  if (imageSettings == null) {\n    return null;\n  }\n  const margin = includeMargin ? MARGIN_SIZE : 0;\n  const numCells = cells.length + margin * 2;\n  const defaultSize = Math.floor(size * DEFAULT_IMG_SCALE);\n  const scale = numCells / size;\n  const w = (imageSettings.width || defaultSize) * scale;\n  const h = (imageSettings.height || defaultSize) * scale;\n  const x = imageSettings.x == null ? cells.length / 2 - w / 2 : imageSettings.x * scale;\n  const y = imageSettings.y == null ? cells.length / 2 - h / 2 : imageSettings.y * scale;\n  let excavation = null;\n  if (imageSettings.excavate) {\n    let floorX = Math.floor(x);\n    let floorY = Math.floor(y);\n    let ceilW = Math.ceil(w + x - floorX);\n    let ceilH = Math.ceil(h + y - floorY);\n    excavation = { x: floorX, y: floorY, w: ceilW, h: ceilH };\n  }\n  return { x, y, h, w, excavation };\n}\nvar SUPPORTS_PATH2D = function() {\n  try {\n    new Path2D().addPath(new Path2D());\n  } catch (e) {\n    return false;\n  }\n  return true;\n}();\nfunction QRCodeCanvas(props) {\n  const _a = props, {\n    value,\n    size = DEFAULT_SIZE,\n    level = DEFAULT_LEVEL,\n    bgColor = DEFAULT_BGCOLOR,\n    fgColor = DEFAULT_FGCOLOR,\n    includeMargin = DEFAULT_INCLUDEMARGIN,\n    style,\n    imageSettings\n  } = _a, otherProps = __objRest(_a, [\n    \"value\",\n    \"size\",\n    \"level\",\n    \"bgColor\",\n    \"fgColor\",\n    \"includeMargin\",\n    \"style\",\n    \"imageSettings\"\n  ]);\n  const imgSrc = imageSettings == null ? void 0 : imageSettings.src;\n  const _canvas = React.useRef(null);\n  const _image = React.useRef(null);\n  const [isImgLoaded, setIsImageLoaded] = React.useState(false);\n  React.useEffect(() => {\n    if (_canvas.current != null) {\n      const canvas = _canvas.current;\n      const ctx = canvas.getContext(\"2d\");\n      if (!ctx) {\n        return;\n      }\n      let cells = qrcodegen_default.QrCode.encodeText(value, ERROR_LEVEL_MAP[level]).getModules();\n      const margin = includeMargin ? MARGIN_SIZE : 0;\n      const numCells = cells.length + margin * 2;\n      const calculatedImageSettings = getImageSettings(cells, size, includeMargin, imageSettings);\n      const image = _image.current;\n      const haveImageToRender = calculatedImageSettings != null && image !== null && image.complete && image.naturalHeight !== 0 && image.naturalWidth !== 0;\n      if (haveImageToRender) {\n        if (calculatedImageSettings.excavation != null) {\n          cells = excavateModules(cells, calculatedImageSettings.excavation);\n        }\n      }\n      const pixelRatio = window.devicePixelRatio || 1;\n      canvas.height = canvas.width = size * pixelRatio;\n      const scale = size / numCells * pixelRatio;\n      ctx.scale(scale, scale);\n      ctx.fillStyle = bgColor;\n      ctx.fillRect(0, 0, numCells, numCells);\n      ctx.fillStyle = fgColor;\n      if (SUPPORTS_PATH2D) {\n        ctx.fill(new Path2D(generatePath(cells, margin)));\n      } else {\n        cells.forEach(function(row, rdx) {\n          row.forEach(function(cell, cdx) {\n            if (cell) {\n              ctx.fillRect(cdx + margin, rdx + margin, 1, 1);\n            }\n          });\n        });\n      }\n      if (haveImageToRender) {\n        ctx.drawImage(image, calculatedImageSettings.x + margin, calculatedImageSettings.y + margin, calculatedImageSettings.w, calculatedImageSettings.h);\n      }\n    }\n  });\n  React.useEffect(() => {\n    setIsImageLoaded(false);\n  }, [imgSrc]);\n  const canvasStyle = __spreadValues({ height: size, width: size }, style);\n  let img = null;\n  if (imgSrc != null) {\n    img = /* @__PURE__ */ React.createElement(\"img\", {\n      src: imgSrc,\n      key: imgSrc,\n      style: { display: \"none\" },\n      onLoad: () => {\n        setIsImageLoaded(true);\n      },\n      ref: _image\n    });\n  }\n  return /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement(\"canvas\", __spreadValues({\n    style: canvasStyle,\n    height: size,\n    width: size,\n    ref: _canvas\n  }, otherProps)), img);\n}\nfunction QRCodeSVG(props) {\n  const _a = props, {\n    value,\n    size = DEFAULT_SIZE,\n    level = DEFAULT_LEVEL,\n    bgColor = DEFAULT_BGCOLOR,\n    fgColor = DEFAULT_FGCOLOR,\n    includeMargin = DEFAULT_INCLUDEMARGIN,\n    imageSettings\n  } = _a, otherProps = __objRest(_a, [\n    \"value\",\n    \"size\",\n    \"level\",\n    \"bgColor\",\n    \"fgColor\",\n    \"includeMargin\",\n    \"imageSettings\"\n  ]);\n  let cells = qrcodegen_default.QrCode.encodeText(value, ERROR_LEVEL_MAP[level]).getModules();\n  const margin = includeMargin ? MARGIN_SIZE : 0;\n  const numCells = cells.length + margin * 2;\n  const calculatedImageSettings = getImageSettings(cells, size, includeMargin, imageSettings);\n  let image = null;\n  if (imageSettings != null && calculatedImageSettings != null) {\n    if (calculatedImageSettings.excavation != null) {\n      cells = excavateModules(cells, calculatedImageSettings.excavation);\n    }\n    image = /* @__PURE__ */ React.createElement(\"image\", {\n      xlinkHref: imageSettings.src,\n      height: calculatedImageSettings.h,\n      width: calculatedImageSettings.w,\n      x: calculatedImageSettings.x + margin,\n      y: calculatedImageSettings.y + margin,\n      preserveAspectRatio: \"none\"\n    });\n  }\n  const fgPath = generatePath(cells, margin);\n  return /* @__PURE__ */ React.createElement(\"svg\", __spreadValues({\n    height: size,\n    width: size,\n    viewBox: `0 0 ${numCells} ${numCells}`\n  }, otherProps), /* @__PURE__ */ React.createElement(\"path\", {\n    fill: bgColor,\n    d: `M0,0 h${numCells}v${numCells}H0z`,\n    shapeRendering: \"crispEdges\"\n  }), /* @__PURE__ */ React.createElement(\"path\", {\n    fill: fgColor,\n    d: fgPath,\n    shapeRendering: \"crispEdges\"\n  }), image);\n}\nvar QRCode = (props) => {\n  const _a = props, { renderAs } = _a, otherProps = __objRest(_a, [\"renderAs\"]);\n  if (renderAs === \"svg\") {\n    return /* @__PURE__ */ React.createElement(QRCodeSVG, __spreadValues({}, otherProps));\n  }\n  return /* @__PURE__ */ React.createElement(QRCodeCanvas, __spreadValues({}, otherProps));\n};\nexport {\n  QRCodeCanvas,\n  QRCodeSVG,\n  QRCode as default\n};\n"], "mappings": ";;;;;;;;AA8BA,mBAAkB;AA9BlB,IAAI,YAAY,OAAO;AACvB,IAAI,sBAAsB,OAAO;AACjC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAI,iBAAiB,CAAC,GAAG,MAAM;AAC7B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,sBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AACpC,MAAI;AACF,aAAS,QAAQ,oBAAoB,CAAC,GAAG;AACvC,UAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,wBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACpC;AACF,SAAO;AACT;AACA,IAAI,YAAY,CAAC,QAAQ,YAAY;AACnC,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ;AACf,QAAI,aAAa,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAC7D,aAAO,IAAI,IAAI,OAAO,IAAI;AAC9B,MAAI,UAAU,QAAQ;AACpB,aAAS,QAAQ,oBAAoB,MAAM,GAAG;AAC5C,UAAI,QAAQ,QAAQ,IAAI,IAAI,KAAK,aAAa,KAAK,QAAQ,IAAI;AAC7D,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC9B;AACF,SAAO;AACT;AAWA,IAAI;AAAA,CACH,CAAC,eAAe;AACf,QAAM,UAAU,MAAM;AAAA,IACpB,YAAY,SAAS,sBAAsB,eAAe,KAAK;AAC7D,WAAK,UAAU;AACf,WAAK,uBAAuB;AAC5B,WAAK,UAAU,CAAC;AAChB,WAAK,aAAa,CAAC;AACnB,UAAI,UAAU,QAAQ,eAAe,UAAU,QAAQ;AACrD,cAAM,IAAI,WAAW,4BAA4B;AACnD,UAAI,MAAM,MAAM,MAAM;AACpB,cAAM,IAAI,WAAW,yBAAyB;AAChD,WAAK,OAAO,UAAU,IAAI;AAC1B,UAAI,MAAM,CAAC;AACX,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM;AAC7B,YAAI,KAAK,KAAK;AAChB,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,aAAK,QAAQ,KAAK,IAAI,MAAM,CAAC;AAC7B,aAAK,WAAW,KAAK,IAAI,MAAM,CAAC;AAAA,MAClC;AACA,WAAK,qBAAqB;AAC1B,YAAM,eAAe,KAAK,oBAAoB,aAAa;AAC3D,WAAK,cAAc,YAAY;AAC/B,UAAI,OAAO,IAAI;AACb,YAAI,aAAa;AACjB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAK,UAAU,CAAC;AAChB,eAAK,eAAe,CAAC;AACrB,gBAAM,UAAU,KAAK,gBAAgB;AACrC,cAAI,UAAU,YAAY;AACxB,kBAAM;AACN,yBAAa;AAAA,UACf;AACA,eAAK,UAAU,CAAC;AAAA,QAClB;AAAA,MACF;AACA,aAAO,KAAK,OAAO,OAAO,CAAC;AAC3B,WAAK,OAAO;AACZ,WAAK,UAAU,GAAG;AAClB,WAAK,eAAe,GAAG;AACvB,WAAK,aAAa,CAAC;AAAA,IACrB;AAAA,IACA,OAAO,WAAW,MAAM,KAAK;AAC3B,YAAM,OAAO,WAAW,UAAU,aAAa,IAAI;AACnD,aAAO,QAAQ,eAAe,MAAM,GAAG;AAAA,IACzC;AAAA,IACA,OAAO,aAAa,MAAM,KAAK;AAC7B,YAAM,MAAM,WAAW,UAAU,UAAU,IAAI;AAC/C,aAAO,QAAQ,eAAe,CAAC,GAAG,GAAG,GAAG;AAAA,IAC1C;AAAA,IACA,OAAO,eAAe,MAAM,KAAK,aAAa,GAAG,aAAa,IAAI,OAAO,IAAI,WAAW,MAAM;AAC5F,UAAI,EAAE,QAAQ,eAAe,cAAc,cAAc,cAAc,cAAc,QAAQ,gBAAgB,OAAO,MAAM,OAAO;AAC/H,cAAM,IAAI,WAAW,eAAe;AACtC,UAAI;AACJ,UAAI;AACJ,WAAK,UAAU,cAAc,WAAW;AACtC,cAAM,oBAAoB,QAAQ,oBAAoB,SAAS,GAAG,IAAI;AACtE,cAAM,WAAW,UAAU,aAAa,MAAM,OAAO;AACrD,YAAI,YAAY,mBAAmB;AACjC,yBAAe;AACf;AAAA,QACF;AACA,YAAI,WAAW;AACb,gBAAM,IAAI,WAAW,eAAe;AAAA,MACxC;AACA,iBAAW,UAAU,CAAC,QAAQ,IAAI,QAAQ,QAAQ,IAAI,UAAU,QAAQ,IAAI,IAAI,GAAG;AACjF,YAAI,YAAY,gBAAgB,QAAQ,oBAAoB,SAAS,MAAM,IAAI;AAC7E,gBAAM;AAAA,MACV;AACA,UAAI,KAAK,CAAC;AACV,iBAAW,OAAO,MAAM;AACtB,mBAAW,IAAI,KAAK,UAAU,GAAG,EAAE;AACnC,mBAAW,IAAI,UAAU,IAAI,KAAK,iBAAiB,OAAO,GAAG,EAAE;AAC/D,mBAAW,KAAK,IAAI,QAAQ;AAC1B,aAAG,KAAK,CAAC;AAAA,MACb;AACA,aAAO,GAAG,UAAU,YAAY;AAChC,YAAM,mBAAmB,QAAQ,oBAAoB,SAAS,GAAG,IAAI;AACrE,aAAO,GAAG,UAAU,gBAAgB;AACpC,iBAAW,GAAG,KAAK,IAAI,GAAG,mBAAmB,GAAG,MAAM,GAAG,EAAE;AAC3D,iBAAW,IAAI,IAAI,GAAG,SAAS,KAAK,GAAG,EAAE;AACzC,aAAO,GAAG,SAAS,KAAK,CAAC;AACzB,eAAS,UAAU,KAAK,GAAG,SAAS,kBAAkB,WAAW,MAAM;AACrE,mBAAW,SAAS,GAAG,EAAE;AAC3B,UAAI,gBAAgB,CAAC;AACrB,aAAO,cAAc,SAAS,IAAI,GAAG;AACnC,sBAAc,KAAK,CAAC;AACtB,SAAG,QAAQ,CAAC,GAAG,MAAM,cAAc,MAAM,CAAC,KAAK,KAAK,KAAK,IAAI,EAAE;AAC/D,aAAO,IAAI,QAAQ,SAAS,KAAK,eAAe,IAAI;AAAA,IACtD;AAAA,IACA,UAAU,GAAG,GAAG;AACd,aAAO,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,CAAC,EAAE,CAAC;AAAA,IAChF;AAAA,IACA,aAAa;AACX,aAAO,KAAK;AAAA,IACd;AAAA,IACA,uBAAuB;AACrB,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,aAAK,kBAAkB,GAAG,GAAG,IAAI,KAAK,CAAC;AACvC,aAAK,kBAAkB,GAAG,GAAG,IAAI,KAAK,CAAC;AAAA,MACzC;AACA,WAAK,kBAAkB,GAAG,CAAC;AAC3B,WAAK,kBAAkB,KAAK,OAAO,GAAG,CAAC;AACvC,WAAK,kBAAkB,GAAG,KAAK,OAAO,CAAC;AACvC,YAAM,cAAc,KAAK,6BAA6B;AACtD,YAAM,WAAW,YAAY;AAC7B,eAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,cAAI,EAAE,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,WAAW,KAAK,KAAK;AACjF,iBAAK,qBAAqB,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAAA,QAC5D;AAAA,MACF;AACA,WAAK,eAAe,CAAC;AACrB,WAAK,YAAY;AAAA,IACnB;AAAA,IACA,eAAe,MAAM;AACnB,YAAM,OAAO,KAAK,qBAAqB,cAAc,IAAI;AACzD,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,IAAI;AACtB,cAAM,OAAO,KAAK,QAAQ,KAAK;AACjC,YAAM,QAAQ,QAAQ,KAAK,OAAO;AAClC,aAAO,SAAS,MAAM,CAAC;AACvB,eAAS,IAAI,GAAG,KAAK,GAAG;AACtB,aAAK,kBAAkB,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAC9C,WAAK,kBAAkB,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAC5C,WAAK,kBAAkB,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAC5C,WAAK,kBAAkB,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAC5C,eAAS,IAAI,GAAG,IAAI,IAAI;AACtB,aAAK,kBAAkB,KAAK,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AACnD,eAAS,IAAI,GAAG,IAAI,GAAG;AACrB,aAAK,kBAAkB,KAAK,OAAO,IAAI,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAC9D,eAAS,IAAI,GAAG,IAAI,IAAI;AACtB,aAAK,kBAAkB,GAAG,KAAK,OAAO,KAAK,GAAG,OAAO,MAAM,CAAC,CAAC;AAC/D,WAAK,kBAAkB,GAAG,KAAK,OAAO,GAAG,IAAI;AAAA,IAC/C;AAAA,IACA,cAAc;AACZ,UAAI,KAAK,UAAU;AACjB;AACF,UAAI,MAAM,KAAK;AACf,eAAS,IAAI,GAAG,IAAI,IAAI;AACtB,cAAM,OAAO,KAAK,QAAQ,MAAM;AAClC,YAAM,OAAO,KAAK,WAAW,KAAK;AAClC,aAAO,SAAS,MAAM,CAAC;AACvB,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAM,QAAQ,OAAO,MAAM,CAAC;AAC5B,cAAM,IAAI,KAAK,OAAO,KAAK,IAAI;AAC/B,cAAM,IAAI,KAAK,MAAM,IAAI,CAAC;AAC1B,aAAK,kBAAkB,GAAG,GAAG,KAAK;AAClC,aAAK,kBAAkB,GAAG,GAAG,KAAK;AAAA,MACpC;AAAA,IACF;AAAA,IACA,kBAAkB,GAAG,GAAG;AACtB,eAAS,KAAK,IAAI,MAAM,GAAG,MAAM;AAC/B,iBAAS,KAAK,IAAI,MAAM,GAAG,MAAM;AAC/B,gBAAM,OAAO,KAAK,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AAChD,gBAAM,KAAK,IAAI;AACf,gBAAM,KAAK,IAAI;AACf,cAAI,KAAK,MAAM,KAAK,KAAK,QAAQ,KAAK,MAAM,KAAK,KAAK;AACpD,iBAAK,kBAAkB,IAAI,IAAI,QAAQ,KAAK,QAAQ,CAAC;AAAA,QACzD;AAAA,MACF;AAAA,IACF;AAAA,IACA,qBAAqB,GAAG,GAAG;AACzB,eAAS,KAAK,IAAI,MAAM,GAAG,MAAM;AAC/B,iBAAS,KAAK,IAAI,MAAM,GAAG;AACzB,eAAK,kBAAkB,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC;AAAA,MACpF;AAAA,IACF;AAAA,IACA,kBAAkB,GAAG,GAAG,QAAQ;AAC9B,WAAK,QAAQ,CAAC,EAAE,CAAC,IAAI;AACrB,WAAK,WAAW,CAAC,EAAE,CAAC,IAAI;AAAA,IAC1B;AAAA,IACA,oBAAoB,MAAM;AACxB,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK;AACjB,UAAI,KAAK,UAAU,QAAQ,oBAAoB,KAAK,GAAG;AACrD,cAAM,IAAI,WAAW,kBAAkB;AACzC,YAAM,YAAY,QAAQ,4BAA4B,IAAI,OAAO,EAAE,GAAG;AACtE,YAAM,cAAc,QAAQ,wBAAwB,IAAI,OAAO,EAAE,GAAG;AACpE,YAAM,eAAe,KAAK,MAAM,QAAQ,qBAAqB,GAAG,IAAI,CAAC;AACrE,YAAM,iBAAiB,YAAY,eAAe;AAClD,YAAM,gBAAgB,KAAK,MAAM,eAAe,SAAS;AACzD,UAAI,SAAS,CAAC;AACd,YAAM,QAAQ,QAAQ,0BAA0B,WAAW;AAC3D,eAAS,IAAI,GAAG,IAAI,GAAG,IAAI,WAAW,KAAK;AACzC,YAAI,MAAM,KAAK,MAAM,GAAG,IAAI,gBAAgB,eAAe,IAAI,iBAAiB,IAAI,EAAE;AACtF,aAAK,IAAI;AACT,cAAM,MAAM,QAAQ,4BAA4B,KAAK,KAAK;AAC1D,YAAI,IAAI;AACN,cAAI,KAAK,CAAC;AACZ,eAAO,KAAK,IAAI,OAAO,GAAG,CAAC;AAAA,MAC7B;AACA,UAAI,SAAS,CAAC;AACd,eAAS,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,QAAQ,KAAK;AACzC,eAAO,QAAQ,CAAC,OAAO,MAAM;AAC3B,cAAI,KAAK,gBAAgB,eAAe,KAAK;AAC3C,mBAAO,KAAK,MAAM,CAAC,CAAC;AAAA,QACxB,CAAC;AAAA,MACH;AACA,aAAO,OAAO,UAAU,YAAY;AACpC,aAAO;AAAA,IACT;AAAA,IACA,cAAc,MAAM;AAClB,UAAI,KAAK,UAAU,KAAK,MAAM,QAAQ,qBAAqB,KAAK,OAAO,IAAI,CAAC;AAC1E,cAAM,IAAI,WAAW,kBAAkB;AACzC,UAAI,IAAI;AACR,eAAS,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS,GAAG;AACtD,YAAI,SAAS;AACX,kBAAQ;AACV,iBAAS,OAAO,GAAG,OAAO,KAAK,MAAM,QAAQ;AAC3C,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,kBAAM,IAAI,QAAQ;AAClB,kBAAM,UAAU,QAAQ,IAAI,MAAM;AAClC,kBAAM,IAAI,SAAS,KAAK,OAAO,IAAI,OAAO;AAC1C,gBAAI,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK,SAAS,GAAG;AACjD,mBAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,OAAO,KAAK,MAAM,CAAC,GAAG,KAAK,IAAI,EAAE;AACtD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO,KAAK,KAAK,SAAS,CAAC;AAAA,IAC7B;AAAA,IACA,UAAU,MAAM;AACd,UAAI,OAAO,KAAK,OAAO;AACrB,cAAM,IAAI,WAAW,yBAAyB;AAChD,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,cAAI;AACJ,kBAAQ,MAAM;AAAA,YACZ,KAAK;AACH,wBAAU,IAAI,KAAK,KAAK;AACxB;AAAA,YACF,KAAK;AACH,uBAAS,IAAI,KAAK;AAClB;AAAA,YACF,KAAK;AACH,uBAAS,IAAI,KAAK;AAClB;AAAA,YACF,KAAK;AACH,wBAAU,IAAI,KAAK,KAAK;AACxB;AAAA,YACF,KAAK;AACH,wBAAU,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,KAAK;AACxD;AAAA,YACF,KAAK;AACH,uBAAS,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AAClC;AAAA,YACF,KAAK;AACH,wBAAU,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK;AACxC;AAAA,YACF,KAAK;AACH,yBAAW,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK;AAC1C;AAAA,YACF;AACE,oBAAM,IAAI,MAAM,aAAa;AAAA,UACjC;AACA,cAAI,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,KAAK;AAC5B,iBAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,QAAQ,CAAC,EAAE,CAAC;AAAA,QAC3C;AAAA,MACF;AAAA,IACF;AAAA,IACA,kBAAkB;AAChB,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,aAAa,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACrC,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,cAAI,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,UAAU;AAClC;AACA,gBAAI,QAAQ;AACV,wBAAU,QAAQ;AAAA,qBACX,OAAO;AACd;AAAA,UACJ,OAAO;AACL,iBAAK,wBAAwB,MAAM,UAAU;AAC7C,gBAAI,CAAC;AACH,wBAAU,KAAK,2BAA2B,UAAU,IAAI,QAAQ;AAClE,uBAAW,KAAK,QAAQ,CAAC,EAAE,CAAC;AAC5B,mBAAO;AAAA,UACT;AAAA,QACF;AACA,kBAAU,KAAK,+BAA+B,UAAU,MAAM,UAAU,IAAI,QAAQ;AAAA,MACtF;AACA,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,aAAa,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACrC,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,cAAI,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,UAAU;AAClC;AACA,gBAAI,QAAQ;AACV,wBAAU,QAAQ;AAAA,qBACX,OAAO;AACd;AAAA,UACJ,OAAO;AACL,iBAAK,wBAAwB,MAAM,UAAU;AAC7C,gBAAI,CAAC;AACH,wBAAU,KAAK,2BAA2B,UAAU,IAAI,QAAQ;AAClE,uBAAW,KAAK,QAAQ,CAAC,EAAE,CAAC;AAC5B,mBAAO;AAAA,UACT;AAAA,QACF;AACA,kBAAU,KAAK,+BAA+B,UAAU,MAAM,UAAU,IAAI,QAAQ;AAAA,MACtF;AACA,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,GAAG,KAAK;AACtC,iBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,GAAG,KAAK;AACtC,gBAAM,QAAQ,KAAK,QAAQ,CAAC,EAAE,CAAC;AAC/B,cAAI,SAAS,KAAK,QAAQ,CAAC,EAAE,IAAI,CAAC,KAAK,SAAS,KAAK,QAAQ,IAAI,CAAC,EAAE,CAAC,KAAK,SAAS,KAAK,QAAQ,IAAI,CAAC,EAAE,IAAI,CAAC;AAC1G,sBAAU,QAAQ;AAAA,QACtB;AAAA,MACF;AACA,UAAI,OAAO;AACX,iBAAW,OAAO,KAAK;AACrB,eAAO,IAAI,OAAO,CAAC,KAAK,UAAU,OAAO,QAAQ,IAAI,IAAI,IAAI;AAC/D,YAAM,QAAQ,KAAK,OAAO,KAAK;AAC/B,YAAM,IAAI,KAAK,KAAK,KAAK,IAAI,OAAO,KAAK,QAAQ,EAAE,IAAI,KAAK,IAAI;AAChE,aAAO,KAAK,KAAK,KAAK,CAAC;AACvB,gBAAU,IAAI,QAAQ;AACtB,aAAO,KAAK,UAAU,UAAU,OAAO;AACvC,aAAO;AAAA,IACT;AAAA,IACA,+BAA+B;AAC7B,UAAI,KAAK,WAAW;AAClB,eAAO,CAAC;AAAA,WACL;AACH,cAAM,WAAW,KAAK,MAAM,KAAK,UAAU,CAAC,IAAI;AAChD,cAAM,OAAO,KAAK,WAAW,KAAK,KAAK,KAAK,MAAM,KAAK,UAAU,IAAI,MAAM,WAAW,IAAI,EAAE,IAAI;AAChG,YAAI,SAAS,CAAC,CAAC;AACf,iBAAS,MAAM,KAAK,OAAO,GAAG,OAAO,SAAS,UAAU,OAAO;AAC7D,iBAAO,OAAO,GAAG,GAAG,GAAG;AACzB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,OAAO,qBAAqB,KAAK;AAC/B,UAAI,MAAM,QAAQ,eAAe,MAAM,QAAQ;AAC7C,cAAM,IAAI,WAAW,6BAA6B;AACpD,UAAI,UAAU,KAAK,MAAM,OAAO,MAAM;AACtC,UAAI,OAAO,GAAG;AACZ,cAAM,WAAW,KAAK,MAAM,MAAM,CAAC,IAAI;AACvC,mBAAW,KAAK,WAAW,MAAM,WAAW;AAC5C,YAAI,OAAO;AACT,oBAAU;AAAA,MACd;AACA,aAAO,OAAO,UAAU,UAAU,KAAK;AACvC,aAAO;AAAA,IACT;AAAA,IACA,OAAO,oBAAoB,KAAK,KAAK;AACnC,aAAO,KAAK,MAAM,QAAQ,qBAAqB,GAAG,IAAI,CAAC,IAAI,QAAQ,wBAAwB,IAAI,OAAO,EAAE,GAAG,IAAI,QAAQ,4BAA4B,IAAI,OAAO,EAAE,GAAG;AAAA,IACrK;AAAA,IACA,OAAO,0BAA0B,QAAQ;AACvC,UAAI,SAAS,KAAK,SAAS;AACzB,cAAM,IAAI,WAAW,qBAAqB;AAC5C,UAAI,SAAS,CAAC;AACd,eAAS,IAAI,GAAG,IAAI,SAAS,GAAG;AAC9B,eAAO,KAAK,CAAC;AACf,aAAO,KAAK,CAAC;AACb,UAAI,OAAO;AACX,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,iBAAO,CAAC,IAAI,QAAQ,oBAAoB,OAAO,CAAC,GAAG,IAAI;AACvD,cAAI,IAAI,IAAI,OAAO;AACjB,mBAAO,CAAC,KAAK,OAAO,IAAI,CAAC;AAAA,QAC7B;AACA,eAAO,QAAQ,oBAAoB,MAAM,CAAC;AAAA,MAC5C;AACA,aAAO;AAAA,IACT;AAAA,IACA,OAAO,4BAA4B,MAAM,SAAS;AAChD,UAAI,SAAS,QAAQ,IAAI,CAAC,MAAM,CAAC;AACjC,iBAAW,KAAK,MAAM;AACpB,cAAM,SAAS,IAAI,OAAO,MAAM;AAChC,eAAO,KAAK,CAAC;AACb,gBAAQ,QAAQ,CAAC,MAAM,MAAM,OAAO,CAAC,KAAK,QAAQ,oBAAoB,MAAM,MAAM,CAAC;AAAA,MACrF;AACA,aAAO;AAAA,IACT;AAAA,IACA,OAAO,oBAAoB,GAAG,GAAG;AAC/B,UAAI,MAAM,KAAK,KAAK,MAAM,KAAK;AAC7B,cAAM,IAAI,WAAW,mBAAmB;AAC1C,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3B,YAAI,KAAK,KAAK,MAAM,KAAK;AACzB,cAAM,MAAM,IAAI,KAAK;AAAA,MACvB;AACA,aAAO,MAAM,KAAK,CAAC;AACnB,aAAO;AAAA,IACT;AAAA,IACA,2BAA2B,YAAY;AACrC,YAAM,IAAI,WAAW,CAAC;AACtB,aAAO,KAAK,KAAK,OAAO,CAAC;AACzB,YAAM,OAAO,IAAI,KAAK,WAAW,CAAC,KAAK,KAAK,WAAW,CAAC,KAAK,IAAI,KAAK,WAAW,CAAC,KAAK,KAAK,WAAW,CAAC,KAAK;AAC7G,cAAQ,QAAQ,WAAW,CAAC,KAAK,IAAI,KAAK,WAAW,CAAC,KAAK,IAAI,IAAI,MAAM,QAAQ,WAAW,CAAC,KAAK,IAAI,KAAK,WAAW,CAAC,KAAK,IAAI,IAAI;AAAA,IACtI;AAAA,IACA,+BAA+B,iBAAiB,kBAAkB,YAAY;AAC5E,UAAI,iBAAiB;AACnB,aAAK,wBAAwB,kBAAkB,UAAU;AACzD,2BAAmB;AAAA,MACrB;AACA,0BAAoB,KAAK;AACzB,WAAK,wBAAwB,kBAAkB,UAAU;AACzD,aAAO,KAAK,2BAA2B,UAAU;AAAA,IACnD;AAAA,IACA,wBAAwB,kBAAkB,YAAY;AACpD,UAAI,WAAW,CAAC,KAAK;AACnB,4BAAoB,KAAK;AAC3B,iBAAW,IAAI;AACf,iBAAW,QAAQ,gBAAgB;AAAA,IACrC;AAAA,EACF;AACA,MAAI,SAAS;AACb,SAAO,cAAc;AACrB,SAAO,cAAc;AACrB,SAAO,aAAa;AACpB,SAAO,aAAa;AACpB,SAAO,aAAa;AACpB,SAAO,aAAa;AACpB,SAAO,0BAA0B;AAAA,IAC/B,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IAClK,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACnK,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACnK,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,EACrK;AACA,SAAO,8BAA8B;AAAA,IACnC,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IAC5I,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACrJ,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACxJ,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,EAC3J;AACA,aAAW,SAAS;AACpB,WAAS,WAAW,KAAK,KAAK,IAAI;AAChC,QAAI,MAAM,KAAK,MAAM,MAAM,QAAQ,OAAO;AACxC,YAAM,IAAI,WAAW,oBAAoB;AAC3C,aAAS,IAAI,MAAM,GAAG,KAAK,GAAG;AAC5B,SAAG,KAAK,QAAQ,IAAI,CAAC;AAAA,EACzB;AACA,WAAS,OAAO,GAAG,GAAG;AACpB,YAAQ,MAAM,IAAI,MAAM;AAAA,EAC1B;AACA,WAAS,OAAO,MAAM;AACpB,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,iBAAiB;AAAA,EACrC;AACA,QAAM,aAAa,MAAM;AAAA,IACvB,YAAY,MAAM,UAAU,SAAS;AACnC,WAAK,OAAO;AACZ,WAAK,WAAW;AAChB,WAAK,UAAU;AACf,UAAI,WAAW;AACb,cAAM,IAAI,WAAW,kBAAkB;AACzC,WAAK,UAAU,QAAQ,MAAM;AAAA,IAC/B;AAAA,IACA,OAAO,UAAU,MAAM;AACrB,UAAI,KAAK,CAAC;AACV,iBAAW,KAAK;AACd,mBAAW,GAAG,GAAG,EAAE;AACrB,aAAO,IAAI,WAAW,WAAW,KAAK,MAAM,KAAK,QAAQ,EAAE;AAAA,IAC7D;AAAA,IACA,OAAO,YAAY,QAAQ;AACzB,UAAI,CAAC,WAAW,UAAU,MAAM;AAC9B,cAAM,IAAI,WAAW,wCAAwC;AAC/D,UAAI,KAAK,CAAC;AACV,eAAS,IAAI,GAAG,IAAI,OAAO,UAAU;AACnC,cAAM,IAAI,KAAK,IAAI,OAAO,SAAS,GAAG,CAAC;AACvC,mBAAW,SAAS,OAAO,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,GAAG,EAAE;AAC3D,aAAK;AAAA,MACP;AACA,aAAO,IAAI,WAAW,WAAW,KAAK,SAAS,OAAO,QAAQ,EAAE;AAAA,IAClE;AAAA,IACA,OAAO,iBAAiB,MAAM;AAC5B,UAAI,CAAC,WAAW,eAAe,IAAI;AACjC,cAAM,IAAI,WAAW,6DAA6D;AACpF,UAAI,KAAK,CAAC;AACV,UAAI;AACJ,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK,GAAG;AACxC,YAAI,OAAO,WAAW,qBAAqB,QAAQ,KAAK,OAAO,CAAC,CAAC,IAAI;AACrE,gBAAQ,WAAW,qBAAqB,QAAQ,KAAK,OAAO,IAAI,CAAC,CAAC;AAClE,mBAAW,MAAM,IAAI,EAAE;AAAA,MACzB;AACA,UAAI,IAAI,KAAK;AACX,mBAAW,WAAW,qBAAqB,QAAQ,KAAK,OAAO,CAAC,CAAC,GAAG,GAAG,EAAE;AAC3E,aAAO,IAAI,WAAW,WAAW,KAAK,cAAc,KAAK,QAAQ,EAAE;AAAA,IACrE;AAAA,IACA,OAAO,aAAa,MAAM;AACxB,UAAI,QAAQ;AACV,eAAO,CAAC;AAAA,eACD,WAAW,UAAU,IAAI;AAChC,eAAO,CAAC,WAAW,YAAY,IAAI,CAAC;AAAA,eAC7B,WAAW,eAAe,IAAI;AACrC,eAAO,CAAC,WAAW,iBAAiB,IAAI,CAAC;AAAA;AAEzC,eAAO,CAAC,WAAW,UAAU,WAAW,gBAAgB,IAAI,CAAC,CAAC;AAAA,IAClE;AAAA,IACA,OAAO,QAAQ,WAAW;AACxB,UAAI,KAAK,CAAC;AACV,UAAI,YAAY;AACd,cAAM,IAAI,WAAW,mCAAmC;AAAA,eACjD,YAAY,KAAK;AACxB,mBAAW,WAAW,GAAG,EAAE;AAAA,eACpB,YAAY,KAAK,IAAI;AAC5B,mBAAW,GAAG,GAAG,EAAE;AACnB,mBAAW,WAAW,IAAI,EAAE;AAAA,MAC9B,WAAW,YAAY,KAAK;AAC1B,mBAAW,GAAG,GAAG,EAAE;AACnB,mBAAW,WAAW,IAAI,EAAE;AAAA,MAC9B;AACE,cAAM,IAAI,WAAW,mCAAmC;AAC1D,aAAO,IAAI,WAAW,WAAW,KAAK,KAAK,GAAG,EAAE;AAAA,IAClD;AAAA,IACA,OAAO,UAAU,MAAM;AACrB,aAAO,WAAW,cAAc,KAAK,IAAI;AAAA,IAC3C;AAAA,IACA,OAAO,eAAe,MAAM;AAC1B,aAAO,WAAW,mBAAmB,KAAK,IAAI;AAAA,IAChD;AAAA,IACA,UAAU;AACR,aAAO,KAAK,QAAQ,MAAM;AAAA,IAC5B;AAAA,IACA,OAAO,aAAa,MAAM,SAAS;AACjC,UAAI,SAAS;AACb,iBAAW,OAAO,MAAM;AACtB,cAAM,SAAS,IAAI,KAAK,iBAAiB,OAAO;AAChD,YAAI,IAAI,YAAY,KAAK;AACvB,iBAAO;AACT,kBAAU,IAAI,SAAS,IAAI,QAAQ;AAAA,MACrC;AACA,aAAO;AAAA,IACT;AAAA,IACA,OAAO,gBAAgB,KAAK;AAC1B,YAAM,UAAU,GAAG;AACnB,UAAI,SAAS,CAAC;AACd,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,IAAI,OAAO,CAAC,KAAK;AACnB,iBAAO,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,aAC1B;AACH,iBAAO,KAAK,SAAS,IAAI,OAAO,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC;AAC9C,eAAK;AAAA,QACP;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,YAAY;AAChB,YAAU,gBAAgB;AAC1B,YAAU,qBAAqB;AAC/B,YAAU,uBAAuB;AACjC,aAAW,YAAY;AACzB,GAAG,cAAc,YAAY,CAAC,EAAE;AAAA,CAC/B,CAAC,eAAe;AACf,MAAI;AACJ,GAAC,CAAC,YAAY;AACZ,UAAM,OAAO,MAAM;AAAA,MACjB,YAAY,SAAS,YAAY;AAC/B,aAAK,UAAU;AACf,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AACA,QAAI,MAAM;AACV,QAAI,MAAM,IAAI,KAAK,GAAG,CAAC;AACvB,QAAI,SAAS,IAAI,KAAK,GAAG,CAAC;AAC1B,QAAI,WAAW,IAAI,KAAK,GAAG,CAAC;AAC5B,QAAI,OAAO,IAAI,KAAK,GAAG,CAAC;AACxB,YAAQ,MAAM;AAAA,EAChB,GAAG,SAAS,WAAW,WAAW,WAAW,SAAS,CAAC,EAAE;AAC3D,GAAG,cAAc,YAAY,CAAC,EAAE;AAAA,CAC/B,CAAC,eAAe;AACf,MAAI;AACJ,GAAC,CAAC,eAAe;AACf,UAAM,QAAQ,MAAM;AAAA,MAClB,YAAY,UAAU,kBAAkB;AACtC,aAAK,WAAW;AAChB,aAAK,mBAAmB;AAAA,MAC1B;AAAA,MACA,iBAAiB,KAAK;AACpB,eAAO,KAAK,iBAAiB,KAAK,OAAO,MAAM,KAAK,EAAE,CAAC;AAAA,MACzD;AAAA,IACF;AACA,QAAI,OAAO;AACX,SAAK,UAAU,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;AACxC,SAAK,eAAe,IAAI,MAAM,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC;AAC5C,SAAK,OAAO,IAAI,MAAM,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC;AACpC,SAAK,QAAQ,IAAI,MAAM,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC;AACrC,SAAK,MAAM,IAAI,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AACjC,eAAW,OAAO;AAAA,EACpB,GAAG,YAAY,WAAW,cAAc,WAAW,YAAY,CAAC,EAAE;AACpE,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI,oBAAoB;AAQxB,IAAI,kBAAkB;AAAA,EACpB,GAAG,kBAAkB,OAAO,IAAI;AAAA,EAChC,GAAG,kBAAkB,OAAO,IAAI;AAAA,EAChC,GAAG,kBAAkB,OAAO,IAAI;AAAA,EAChC,GAAG,kBAAkB,OAAO,IAAI;AAClC;AACA,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,wBAAwB;AAC5B,IAAI,cAAc;AAClB,IAAI,oBAAoB;AACxB,SAAS,aAAa,SAAS,SAAS,GAAG;AACzC,QAAM,MAAM,CAAC;AACb,UAAQ,QAAQ,SAAS,KAAK,GAAG;AAC/B,QAAI,QAAQ;AACZ,QAAI,QAAQ,SAAS,MAAM,GAAG;AAC5B,UAAI,CAAC,QAAQ,UAAU,MAAM;AAC3B,YAAI,KAAK,IAAI,QAAQ,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,KAAK,MAAM,QAAQ,MAAM,GAAG;AAC7E,gBAAQ;AACR;AAAA,MACF;AACA,UAAI,MAAM,IAAI,SAAS,GAAG;AACxB,YAAI,CAAC,MAAM;AACT;AAAA,QACF;AACA,YAAI,UAAU,MAAM;AAClB,cAAI,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,SAAS,IAAI,MAAM,GAAG;AAAA,QAC7D,OAAO;AACL,cAAI,KAAK,IAAI,QAAQ,MAAM,IAAI,IAAI,MAAM,KAAK,IAAI,IAAI,KAAK,MAAM,QAAQ,MAAM,GAAG;AAAA,QACpF;AACA;AAAA,MACF;AACA,UAAI,QAAQ,UAAU,MAAM;AAC1B,gBAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO,IAAI,KAAK,EAAE;AACpB;AACA,SAAS,gBAAgB,SAAS,YAAY;AAC5C,SAAO,QAAQ,MAAM,EAAE,IAAI,CAAC,KAAK,MAAM;AACrC,QAAI,IAAI,WAAW,KAAK,KAAK,WAAW,IAAI,WAAW,GAAG;AACxD,aAAO;AAAA,IACT;AACA,WAAO,IAAI,IAAI,CAAC,MAAM,MAAM;AAC1B,UAAI,IAAI,WAAW,KAAK,KAAK,WAAW,IAAI,WAAW,GAAG;AACxD,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH,CAAC;AACH;AACA,SAAS,iBAAiB,OAAO,MAAM,eAAe,eAAe;AACnE,MAAI,iBAAiB,MAAM;AACzB,WAAO;AAAA,EACT;AACA,QAAM,SAAS,gBAAgB,cAAc;AAC7C,QAAM,WAAW,MAAM,SAAS,SAAS;AACzC,QAAM,cAAc,KAAK,MAAM,OAAO,iBAAiB;AACvD,QAAM,QAAQ,WAAW;AACzB,QAAM,KAAK,cAAc,SAAS,eAAe;AACjD,QAAM,KAAK,cAAc,UAAU,eAAe;AAClD,QAAM,IAAI,cAAc,KAAK,OAAO,MAAM,SAAS,IAAI,IAAI,IAAI,cAAc,IAAI;AACjF,QAAM,IAAI,cAAc,KAAK,OAAO,MAAM,SAAS,IAAI,IAAI,IAAI,cAAc,IAAI;AACjF,MAAI,aAAa;AACjB,MAAI,cAAc,UAAU;AAC1B,QAAI,SAAS,KAAK,MAAM,CAAC;AACzB,QAAI,SAAS,KAAK,MAAM,CAAC;AACzB,QAAI,QAAQ,KAAK,KAAK,IAAI,IAAI,MAAM;AACpC,QAAI,QAAQ,KAAK,KAAK,IAAI,IAAI,MAAM;AACpC,iBAAa,EAAE,GAAG,QAAQ,GAAG,QAAQ,GAAG,OAAO,GAAG,MAAM;AAAA,EAC1D;AACA,SAAO,EAAE,GAAG,GAAG,GAAG,GAAG,WAAW;AAClC;AACA,IAAI,kBAAkB,WAAW;AAC/B,MAAI;AACF,QAAI,OAAO,EAAE,QAAQ,IAAI,OAAO,CAAC;AAAA,EACnC,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACA,SAAO;AACT,EAAE;AACF,SAAS,aAAa,OAAO;AAC3B,QAAM,KAAK,OAAO;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB;AAAA,IACA;AAAA,EACF,IAAI,IAAI,aAAa,UAAU,IAAI;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,SAAS,iBAAiB,OAAO,SAAS,cAAc;AAC9D,QAAM,UAAU,aAAAA,QAAM,OAAO,IAAI;AACjC,QAAM,SAAS,aAAAA,QAAM,OAAO,IAAI;AAChC,QAAM,CAAC,aAAa,gBAAgB,IAAI,aAAAA,QAAM,SAAS,KAAK;AAC5D,eAAAA,QAAM,UAAU,MAAM;AACpB,QAAI,QAAQ,WAAW,MAAM;AAC3B,YAAM,SAAS,QAAQ;AACvB,YAAM,MAAM,OAAO,WAAW,IAAI;AAClC,UAAI,CAAC,KAAK;AACR;AAAA,MACF;AACA,UAAI,QAAQ,kBAAkB,OAAO,WAAW,OAAO,gBAAgB,KAAK,CAAC,EAAE,WAAW;AAC1F,YAAM,SAAS,gBAAgB,cAAc;AAC7C,YAAM,WAAW,MAAM,SAAS,SAAS;AACzC,YAAM,0BAA0B,iBAAiB,OAAO,MAAM,eAAe,aAAa;AAC1F,YAAM,QAAQ,OAAO;AACrB,YAAM,oBAAoB,2BAA2B,QAAQ,UAAU,QAAQ,MAAM,YAAY,MAAM,kBAAkB,KAAK,MAAM,iBAAiB;AACrJ,UAAI,mBAAmB;AACrB,YAAI,wBAAwB,cAAc,MAAM;AAC9C,kBAAQ,gBAAgB,OAAO,wBAAwB,UAAU;AAAA,QACnE;AAAA,MACF;AACA,YAAM,aAAa,OAAO,oBAAoB;AAC9C,aAAO,SAAS,OAAO,QAAQ,OAAO;AACtC,YAAM,QAAQ,OAAO,WAAW;AAChC,UAAI,MAAM,OAAO,KAAK;AACtB,UAAI,YAAY;AAChB,UAAI,SAAS,GAAG,GAAG,UAAU,QAAQ;AACrC,UAAI,YAAY;AAChB,UAAI,iBAAiB;AACnB,YAAI,KAAK,IAAI,OAAO,aAAa,OAAO,MAAM,CAAC,CAAC;AAAA,MAClD,OAAO;AACL,cAAM,QAAQ,SAAS,KAAK,KAAK;AAC/B,cAAI,QAAQ,SAAS,MAAM,KAAK;AAC9B,gBAAI,MAAM;AACR,kBAAI,SAAS,MAAM,QAAQ,MAAM,QAAQ,GAAG,CAAC;AAAA,YAC/C;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,UAAI,mBAAmB;AACrB,YAAI,UAAU,OAAO,wBAAwB,IAAI,QAAQ,wBAAwB,IAAI,QAAQ,wBAAwB,GAAG,wBAAwB,CAAC;AAAA,MACnJ;AAAA,IACF;AAAA,EACF,CAAC;AACD,eAAAA,QAAM,UAAU,MAAM;AACpB,qBAAiB,KAAK;AAAA,EACxB,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,cAAc,eAAe,EAAE,QAAQ,MAAM,OAAO,KAAK,GAAG,KAAK;AACvE,MAAI,MAAM;AACV,MAAI,UAAU,MAAM;AAClB,UAAsB,aAAAA,QAAM,cAAc,OAAO;AAAA,MAC/C,KAAK;AAAA,MACL,KAAK;AAAA,MACL,OAAO,EAAE,SAAS,OAAO;AAAA,MACzB,QAAQ,MAAM;AACZ,yBAAiB,IAAI;AAAA,MACvB;AAAA,MACA,KAAK;AAAA,IACP,CAAC;AAAA,EACH;AACA,SAAuB,aAAAA,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAsB,aAAAA,QAAM,cAAc,UAAU,eAAe;AAAA,IAC5H,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,KAAK;AAAA,EACP,GAAG,UAAU,CAAC,GAAG,GAAG;AACtB;AACA,SAAS,UAAU,OAAO;AACxB,QAAM,KAAK,OAAO;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB;AAAA,EACF,IAAI,IAAI,aAAa,UAAU,IAAI;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,QAAQ,kBAAkB,OAAO,WAAW,OAAO,gBAAgB,KAAK,CAAC,EAAE,WAAW;AAC1F,QAAM,SAAS,gBAAgB,cAAc;AAC7C,QAAM,WAAW,MAAM,SAAS,SAAS;AACzC,QAAM,0BAA0B,iBAAiB,OAAO,MAAM,eAAe,aAAa;AAC1F,MAAI,QAAQ;AACZ,MAAI,iBAAiB,QAAQ,2BAA2B,MAAM;AAC5D,QAAI,wBAAwB,cAAc,MAAM;AAC9C,cAAQ,gBAAgB,OAAO,wBAAwB,UAAU;AAAA,IACnE;AACA,YAAwB,aAAAA,QAAM,cAAc,SAAS;AAAA,MACnD,WAAW,cAAc;AAAA,MACzB,QAAQ,wBAAwB;AAAA,MAChC,OAAO,wBAAwB;AAAA,MAC/B,GAAG,wBAAwB,IAAI;AAAA,MAC/B,GAAG,wBAAwB,IAAI;AAAA,MAC/B,qBAAqB;AAAA,IACvB,CAAC;AAAA,EACH;AACA,QAAM,SAAS,aAAa,OAAO,MAAM;AACzC,SAAuB,aAAAA,QAAM,cAAc,OAAO,eAAe;AAAA,IAC/D,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS,OAAO,QAAQ,IAAI,QAAQ;AAAA,EACtC,GAAG,UAAU,GAAmB,aAAAA,QAAM,cAAc,QAAQ;AAAA,IAC1D,MAAM;AAAA,IACN,GAAG,SAAS,QAAQ,IAAI,QAAQ;AAAA,IAChC,gBAAgB;AAAA,EAClB,CAAC,GAAmB,aAAAA,QAAM,cAAc,QAAQ;AAAA,IAC9C,MAAM;AAAA,IACN,GAAG;AAAA,IACH,gBAAgB;AAAA,EAClB,CAAC,GAAG,KAAK;AACX;AACA,IAAI,SAAS,CAAC,UAAU;AACtB,QAAM,KAAK,OAAO,EAAE,SAAS,IAAI,IAAI,aAAa,UAAU,IAAI,CAAC,UAAU,CAAC;AAC5E,MAAI,aAAa,OAAO;AACtB,WAAuB,aAAAA,QAAM,cAAc,WAAW,eAAe,CAAC,GAAG,UAAU,CAAC;AAAA,EACtF;AACA,SAAuB,aAAAA,QAAM,cAAc,cAAc,eAAe,CAAC,GAAG,UAAU,CAAC;AACzF;", "names": ["React"]}